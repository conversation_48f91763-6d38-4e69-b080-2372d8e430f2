import {Routes} from '@angular/router';
import { authGuard } from './guards/auth.guard';
import { adminGuard } from './guards/admin.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'menu/dashboard',
    pathMatch: 'full',
  },
  {
    path: 'home',
    loadComponent: () => import('./home/<USER>').then((m) => m.HomePage),
  },
  {
    path: 'login',
    loadComponent: () => import('./pages/login/login.page').then(m => m.LoginPage)
  },
  {
    path: 'signup',
    loadComponent: () => import('./pages/signup/signup.page').then(m => m.SignupPage)
  },
  {
    path: 'menu',
    loadComponent: () => import('./pages/menu/menu.page').then(m => m.MenuPage),
    canActivate: [authGuard], // Protect the whole menu section
    children: [
      {
        path: 'dashboard',
        loadComponent: () => import('./pages/dashboard/dashboard.page').then(m => m.DashboardPage)
      },
      {
        path: 'requests',
        loadComponent: () => import('./pages/requests/requests.page').then(m => m.RequestsPage)
      },
      {
        path: 'approvals',
        loadComponent: () => import('./pages/approvals/approvals.page').then(m => m.ApprovalsPage)
      },
      {
        path: 'users',
        loadComponent: () => import('./pages/users/users.page').then(m => m.UsersPage),
        canActivate: [adminGuard] // Protect users page with admin guard
      },
      {
        path: 'roles',
        loadComponent: () => import('./pages/roles/roles.page').then(m => m.RolesPage),
        canActivate: [adminGuard] // Protect roles page with admin guard
      },
      {
        path: 'profile',
        loadComponent: () => import('./pages/profile/profile.page').then(m => m.ProfilePage)
      },
      {
        path: 'settings',
        loadComponent: () => import('./pages/settings/settings.page').then(m => m.SettingsPage)
      },
      {
        path: 'expense-form',
        loadComponent: () => import('./pages/expense-form/expense-form.page').then(m => m.ExpenseFormPage)
      },
      {
        path: 'request-details/:id',
        loadComponent: () => import('./pages/request-details/request-details.page').then( m => m.RequestDetailsPage)
      },
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      }
    ]
  }
];
