export interface RequestUser {
  uuid: string;
  username: string;
  fullName: string;
  email: string;
}

export interface Request {
  uuid: string;
  title: string;
  description: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'COMPLETED';
  requestedDate: string;
  approvedDate?: string;
  approvalComment?: string;
  updatedAt?: string;
  requestedBy?: RequestUser;
  approvedBy?: RequestUser;
}

// DTO for request creation
export interface RequestCreateDto {
  title: string;
  description: string;
}
