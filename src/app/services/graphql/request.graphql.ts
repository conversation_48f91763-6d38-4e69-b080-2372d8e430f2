import {gql} from 'apollo-angular';

// Fragment for user fields to reuse across queries
const REQUEST_USER_FIELDS = gql`
  fragment RequestUserFields on RequestUser {
    uuid
    username
    fullName
    email
  }
`;

// Fragment for request fields to reuse across queries
const REQUEST_FIELDS = gql`
  fragment RequestFields on Request {
    uuid
    title
    description
    status
    requestedDate
    approvedDate
    approvalComment
    updatedAt
    requestedBy {
      ...RequestUserFields
    }
    approvedBy {
      ...RequestUserFields
    }
  }
  ${REQUEST_USER_FIELDS}
`;

// Fragment for API response wrapper
const API_RESPONSE_FIELDS = gql`
  fragment ApiResponseFields on ApiResponse {
    status
    code
    errorDescription
    fieldsErrors
  }
`;

// Queries
export const GET_MY_REQUESTS = gql`
  query GetMyRequests {
    getMyRequests {
      status
      code
      errorDescription
      dataList {
        uuid
        title
        description
        status
        requestedDate
      }
      extras
    }
  }
`;

export const GET_ALL_REQUESTS = gql`
  query GetAllRequests {
    getAllRequests {
      status
      code
      errorDescription
      dataList {
        uuid
        title
        description
        status
        requestedDate
      }
      extras
    }
  }
`;

// Mutations
export const CREATE_REQUEST = gql`
  mutation CreateRequest($title: String!, $description: String!) {
    createRequest(requestDto: { title: $title, description: $description }) {
      status
      code
      errorDescription
      data {
        uuid
        title
        description
        status
        requestedDate
      }
    }
  }
`;

export const UPDATE_REQUEST = gql`
  mutation UpdateRequest($uuid: String!, $title: String!, $description: String!) {
    updateRequest(requestDto: { uuid: $uuid, title: $title, description: $description }) {
      status
      code
      errorDescription
      data {
        uuid
        title
        description
        status
        requestedDate
      }
    }
  }
`;

export const APPROVE_REQUEST = gql`
  mutation ApproveRequest($requestUuid: String!, $decision: String!, $comment: String!) {
    approveRequest(approvalDto: { requestUuid: $requestUuid, decision: $decision, comment: $comment }) {
      status
      code
      errorDescription
      data {
        uuid
        title
        description
        status
        requestedDate
        approvedDate
        approvalComment
      }
    }
  }
`;

export const DELETE_REQUEST = gql`
  mutation DeleteRequest($uuid: String!) {
    deleteRequest(uuid: $uuid) {
      status
      code
      errorDescription
      data
    }
  }
`;

export const SAVE_REQUEST = gql`
  mutation SaveRequest($title: String!, $description: String!) {
    saveRequest(requestDto: { title: $title, description: $description }) {
      status
      code
      errorDescription
      data {
        uuid
        title
        description
        status
        requestedDate
        requestedBy {
          uuid
          username
          fullName
          email
        }
      }
    }
  }
`;
