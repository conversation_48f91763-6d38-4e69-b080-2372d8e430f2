import { Injectable } from '@angular/core';
import { Apollo } from 'apollo-angular';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import { User } from '../models/user.model';
import {
  GET_USERS,
  GET_USER_BY_ID,
  SEARCH_USERS,
  CREATE_USER,
  UPDATE_USER,
  DELETE_USER,
  TOGGLE_USER_STATUS,
  ASSIGN_ROLE
} from './graphql/user.graphql';

export interface CreateUserInput {
  name: string;
  email: string;
  role: 'Admin' | 'Manager' | 'Technician';
  department?: string;
  password: string;
}

export interface UpdateUserInput {
  name?: string;
  email?: string;
  role?: 'Admin' | 'Manager' | 'Technician';
  department?: string;
  isActive?: boolean;
}

export interface UserFilters {
  search?: string;
  role?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
}

export interface PaginatedUsersResponse {
  users: User[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private usersSubject = new BehaviorSubject<User[]>([]);
  public users$ = this.usersSubject.asObservable();

  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor(private apollo: Apollo) {}

  // Get all users with optional filters and pagination
  getUsers(filters?: UserFilters): Observable<PaginatedUsersResponse> {
    this.loadingSubject.next(true);

    const variables = {
      ...filters,
      page: filters?.page || 1,
      limit: filters?.limit || 20
    };

    return this.apollo.watchQuery<{ getUsersPaginated: PaginatedUsersResponse }>({
      query: GET_USERS,
      variables,
      fetchPolicy: 'network-only'
    }).valueChanges.pipe(
      map(result => result.data.getUsersPaginated),
      tap(response => {
        if (variables.page === 1) {
          // First page - replace users
          this.usersSubject.next(response.users);
        } else {
          // Subsequent pages - append users
          const currentUsers = this.usersSubject.value;
          this.usersSubject.next([...currentUsers, ...response.users]);
        }
        this.loadingSubject.next(false);
      })
    );
  }

  // Get user by ID
  getUserById(id: string): Observable<User> {
    return this.apollo.watchQuery<{ user: User }>({
      query: GET_USER_BY_ID,
      variables: { id },
      fetchPolicy: 'network-only'
    }).valueChanges.pipe(
      map(result => result.data.user)
    );
  }

  // Search users
  searchUsers(query: string): Observable<User[]> {
    if (!query.trim()) {
      return this.getUsers().pipe(
        map(response => response.users)
      );
    }

    return this.apollo.watchQuery<{ searchUsers: User[] }>({
      query: SEARCH_USERS,
      variables: { query },
      fetchPolicy: 'network-only'
    }).valueChanges.pipe(
      map(result => result.data.searchUsers),
      tap(users => this.usersSubject.next(users))
    );
  }

  // Create new user
  createUser(input: CreateUserInput): Observable<User> {
    return this.apollo.mutate<{ createUser: User }>({
      mutation: CREATE_USER,
      variables: { input },
      refetchQueries: [{ query: GET_USERS }]
    }).pipe(
      map(result => result.data!.createUser),
      tap(() => this.refreshUsers())
    );
  }

  // Update user
  updateUser(id: string, input: UpdateUserInput): Observable<User> {
    return this.apollo.mutate<{ updateUser: User }>({
      mutation: UPDATE_USER,
      variables: { id, input },
      refetchQueries: [{ query: GET_USERS }]
    }).pipe(
      map(result => result.data!.updateUser),
      tap(() => this.refreshUsers())
    );
  }

  // Delete user
  deleteUser(id: string): Observable<{ success: boolean; message: string }> {
    return this.apollo.mutate<{ deleteUser: { success: boolean; message: string } }>({
      mutation: DELETE_USER,
      variables: { id },
      refetchQueries: [{ query: GET_USERS }]
    }).pipe(
      map(result => result.data!.deleteUser),
      tap(() => this.refreshUsers())
    );
  }

  // Toggle user active status
  toggleUserStatus(id: string): Observable<User> {
    return this.apollo.mutate<{ toggleUserStatus: User }>({
      mutation: TOGGLE_USER_STATUS,
      variables: { id },
      refetchQueries: [{ query: GET_USERS }]
    }).pipe(
      map(result => result.data!.toggleUserStatus),
      tap(() => this.refreshUsers())
    );
  }

  // Assign role to user
  assignRole(userId: string, role: string): Observable<User> {
    return this.apollo.mutate<{ assignRole: User }>({
      mutation: ASSIGN_ROLE,
      variables: { userId, role },
      refetchQueries: [{ query: GET_USERS }]
    }).pipe(
      map(result => result.data!.assignRole),
      tap(() => this.refreshUsers())
    );
  }

  // Refresh users list
  private refreshUsers(): void {
    this.getUsers().subscribe({
      next: (response) => {
        // Users are already updated in the getUsers method via tap
        console.log('Users refreshed:', response.users.length);
      },
      error: (error) => {
        console.error('Error refreshing users:', error);
      }
    });
  }

  // Get current users from subject
  getCurrentUsers(): User[] {
    return this.usersSubject.value;
  }
}
