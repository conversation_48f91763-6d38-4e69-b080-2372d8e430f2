import { Injectable } from '@angular/core';
import { AuthService } from './auth/auth.service';
import { NgxPermissionsService } from 'ngx-permissions';

@Injectable({
  providedIn: 'root'
})
export class PermissionInitService {

  private isInitialized = false;

  constructor(
    private authService: AuthService,
    private ngxPermissionsService: NgxPermissionsService
  ) {}

  /**
   * Initialize permissions on app startup
   * This should be called before any components that use permission directives are rendered
   */
  async initializePermissions(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      console.log('Initializing permissions...');
      
      // First, ensure auth status is initialized
      await this.authService.initializeAuthStatus();
      
      // If user is authenticated, ensure permissions are loaded
      if (this.authService.isAuthenticated()) {
        // Check if permissions are already loaded
        if (!this.authService.hasPermissionsLoaded()) {
          console.log('User is authenticated but permissions not loaded, attempting to restore...');
          
          // Try to restore from storage first
          const restored = await this.restorePermissionsFromStorage();
          
          if (!restored) {
            console.log('Could not restore from storage, fetching from server...');
            // If restoration fails, try to fetch fresh permissions from server
            try {
              await this.authService.refreshPermissions();
            } catch (error) {
              console.error('Failed to fetch permissions from server:', error);
              // If server fetch fails, user might need to re-login
              return false;
            }
          }
        }
      } else {
        // User is not authenticated, clear any stale permissions
        this.ngxPermissionsService.flushPermissions();
      }

      this.isInitialized = true;
      console.log('Permission initialization completed');
      return true;
    } catch (error) {
      console.error('Error during permission initialization:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Restore permissions from storage without making server calls
   */
  private async restorePermissionsFromStorage(): Promise<boolean> {
    try {
      const permissionsKey = 'userPermissions';

      // Try localStorage first (faster)
      let permissions: string[] | null = null;
      const localStoragePermissions = localStorage.getItem(permissionsKey);

      if (localStoragePermissions) {
        try {
          permissions = JSON.parse(localStoragePermissions);
        } catch (parseError) {
          console.error('Error parsing permissions from localStorage:', parseError);
          localStorage.removeItem(permissionsKey); // Clear corrupted data
        }
      }

      if (permissions && Array.isArray(permissions) && permissions.length > 0) {
        // Validate that all permissions start with ROLE_ prefix
        const validPermissions = permissions.filter(p =>
          typeof p === 'string' && p.startsWith('ROLE_')
        );

        if (validPermissions.length > 0) {
          this.ngxPermissionsService.loadPermissions(validPermissions);
          console.log('Permissions restored from storage:', validPermissions);
          return true;
        } else {
          console.warn('No valid permissions found in storage (must start with ROLE_)');
        }
      } else {
        console.log('No permissions found in localStorage');
      }

      return false;
    } catch (error) {
      console.error('Error restoring permissions from storage:', error);
      return false;
    }
  }

  /**
   * Reset initialization state (useful for testing or re-initialization)
   */
  reset(): void {
    this.isInitialized = false;
  }

  /**
   * Check if permissions have been initialized
   */
  isPermissionInitialized(): boolean {
    return this.isInitialized;
  }
}
