import {createReducer, on} from '@ngrx/store';
import {createEntityAdapter, EntityAdapter, EntityState} from '@ngrx/entity';
import {User, UserFilterDto} from '../../models/user.model';
import * as UserActions from './user.actions';

export interface UserState extends EntityState<User> {
  selectedUserUuid: string | null;
  loading: boolean;
  error: any;
  filters: UserFilterDto;
  totalCount: number;
  currentPage: number;
  totalPages: number;
  pageSize: number;
}

export const adapter: EntityAdapter<User> = createEntityAdapter<User>({
  selectId: (user: User) => user.uuid,
  sortComparer: (a: User, b: User) => {
    // Sort by fullName alphabetically
    return a.fullName.localeCompare(b.fullName);
  },
});

export const initialState: UserState = adapter.getInitialState({
  selectedUserUuid: null,
  loading: false,
  error: null,
  filters: {},
  totalCount: 0,
  currentPage: 0,
  totalPages: 0,
  pageSize: 10
});

export const userReducer = createReducer(
  initialState,

  // Load Users
  on(UserActions.loadUsers, (state, { filters }) => ({
    ...state,
    loading: true,
    error: null,
    filters: filters || {}
  })),

  on(UserActions.loadUsersSuccess, (state, { response }) =>
    adapter.setAll(response.users, {
      ...state,
      loading: false,
      totalCount: response.totalCount,
      currentPage: response.page,
      totalPages: response.totalPages,
      pageSize: response.size
    })
  ),

  on(UserActions.loadUsersFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Load User by UUID
  on(UserActions.loadUserByUuid, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(UserActions.loadUserByUuidSuccess, (state, { user }) =>
    adapter.upsertOne(user, {
      ...state,
      selectedUserUuid: user.uuid,
      loading: false
    })
  ),

  on(UserActions.loadUserByUuidFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Save User (Create/Update)
  on(UserActions.saveUser, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(UserActions.saveUserSuccess, (state, { user }) =>
    adapter.upsertOne(user, {
      ...state,
      loading: false
    })
  ),

  on(UserActions.saveUserFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Delete User
  on(UserActions.deleteUser, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(UserActions.deleteUserSuccess, (state, { uuid }) =>
    adapter.removeOne(uuid, {
      ...state,
      loading: false
    })
  ),

  on(UserActions.deleteUserFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Deactivate User
  on(UserActions.deactivateUser, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(UserActions.deactivateUserSuccess, (state, { user }) =>
    adapter.updateOne(
      { id: user.uuid, changes: user },
      {
        ...state,
        loading: false
      }
    )
  ),

  on(UserActions.deactivateUserFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Activate User
  on(UserActions.activateUser, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(UserActions.activateUserSuccess, (state, { user }) =>
    adapter.updateOne(
      { id: user.uuid, changes: user },
      {
        ...state,
        loading: false
      }
    )
  ),

  on(UserActions.activateUserFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Assign Roles to User
  on(UserActions.assignRolesToUser, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(UserActions.assignRolesToUserSuccess, (state, { user }) =>
    adapter.updateOne(
      { id: user.uuid, changes: user },
      {
        ...state,
        loading: false
      }
    )
  ),

  on(UserActions.assignRolesToUserFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Clear Error
  on(UserActions.clearUserError, (state) => ({
    ...state,
    error: null
  })),

  // Set Selected User
  on(UserActions.setSelectedUser, (state, { uuid }) => ({
    ...state,
    selectedUserUuid: uuid
  })),

  // Set User Filters
  on(UserActions.setUserFilters, (state, { filters }) => ({
    ...state,
    filters
  }))
);

// Export the entity selectors
export const {
  selectIds,
  selectEntities,
  selectAll,
  selectTotal,
} = adapter.getSelectors();
