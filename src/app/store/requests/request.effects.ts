import {Injectable} from '@angular/core';
import {Actions, createEffect, ofType} from '@ngrx/effects';
import {of} from 'rxjs';
import {catchError, map, mergeMap} from 'rxjs/operators';
import {GraphqlService} from '../../services/graphql.service';
import * as RequestActions from './request.actions';

@Injectable()
export class RequestEffects {

  loadRequests$ = createEffect(() => this.actions$.pipe(
    ofType(RequestActions.loadRequests),
    mergeMap(() => this.graphqlService.getMyRequests()
      .pipe(
        map(response => {
          console.log('Raw GraphQL Response:', response);
          // Extract requests from the new API response format
          const requests = response && response.status && response.dataList ? response.dataList : [];
          console.log('Extracted requests:', requests);
          return RequestActions.loadRequestsSuccess({ requests });
        }),
        catchError(error => {
          console.error('GraphQL Error in effect:', error);
          return of(RequestActions.loadRequestsFailure({ error }));
        })
      )
    )
  ));

  loadRequestById$ = createEffect(() => this.actions$.pipe(
    ofType(RequestActions.loadRequestById),
    mergeMap(({ requestId }) => {
      // Since getRequestById doesn't exist in new API, we'll get all requests and filter
      return this.graphqlService.getMyRequests()
        .pipe(
          map(response => {
            if (response.status && response.dataList) {
              const request = response.dataList.find((req: any) => req.uuid === requestId);
              if (request) {
                return RequestActions.loadRequestByIdSuccess({ request });
              } else {
                return RequestActions.loadRequestByIdFailure({ error: 'Request not found' });
              }
            }
            return RequestActions.loadRequestByIdFailure({ error: 'Failed to load requests' });
          }),
          catchError(error => of(RequestActions.loadRequestByIdFailure({ error })))
        );
    })
  ));

  createRequest$ = createEffect(() => this.actions$.pipe(
    ofType(RequestActions.createRequest),
    mergeMap(({ request }) => {
      // Transform request to match new API DTO structure
      const requestDto = {
        title: request.title || '',
        description: request.description || ''
      };
      return this.graphqlService.createRequest(requestDto)
        .pipe(
          map(response => {
            if (response.status && response.data) {
              return RequestActions.createRequestSuccess({ request: response.data });
            } else {
              return RequestActions.createRequestFailure({ error: response.errorDescription || 'Create failed' });
            }
          }),
          catchError(error => of(RequestActions.createRequestFailure({ error })))
        );
    })
  ));

  updateRequest$ = createEffect(() => this.actions$.pipe(
    ofType(RequestActions.updateRequest),
    mergeMap(({ request }) => {
      // Transform request to match new API DTO structure
      const requestDto = {
        uuid: request.uuid,
        title: request.title || '',
        description: request.description || ''
      };
      return this.graphqlService.updateRequest(requestDto)
        .pipe(
          map(response => {
            if (response.status && response.data) {
              return RequestActions.updateRequestSuccess({ request: response.data });
            } else {
              return RequestActions.updateRequestFailure({ error: response.errorDescription || 'Update failed' });
            }
          }),
          catchError(error => of(RequestActions.updateRequestFailure({ error })))
        );
    })
  ));

  approveRequest$ = createEffect(() => this.actions$.pipe(
    ofType(RequestActions.approveRequest),
    mergeMap(({ requestId, approverName }) => {
      // Transform to new API approval DTO structure
      const approvalDto = {
        requestUuid: requestId,
        decision: 'APPROVED',
        comment: `Approved by ${approverName}`
      };
      return this.graphqlService.approveRequest(approvalDto)
        .pipe(
          map(response => {
            if (response.status && response.data) {
              return RequestActions.approveRequestSuccess({ request: response.data });
            } else {
              return RequestActions.approveRequestFailure({ error: response.errorDescription || 'Approval failed' });
            }
          }),
          catchError(error => of(RequestActions.approveRequestFailure({ error })))
        );
    })
  ));

  rejectRequest$ = createEffect(() => this.actions$.pipe(
    ofType(RequestActions.rejectRequest),
    mergeMap(({ requestId, approverName, remarks }) => {
      // Transform to new API approval DTO structure (reject uses same endpoint)
      const approvalDto = {
        requestUuid: requestId,
        decision: 'REJECTED',
        comment: remarks || `Rejected by ${approverName}`
      };
      return this.graphqlService.approveRequest(approvalDto)
        .pipe(
          map(response => {
            if (response.status && response.data) {
              return RequestActions.rejectRequestSuccess({ request: response.data });
            } else {
              return RequestActions.rejectRequestFailure({ error: response.errorDescription || 'Rejection failed' });
            }
          }),
          catchError(error => of(RequestActions.rejectRequestFailure({ error })))
        );
    })
  ));

  completeRequest$ = createEffect(() => this.actions$.pipe(
    ofType(RequestActions.completeRequest),
    mergeMap(({ requestId }) => {
      // Complete functionality is now handled by approval with APPROVED status
      // We'll use the delete functionality or skip this effect
      // For now, let's make it a no-op that returns success
      return of(RequestActions.completeRequestSuccess({ request: { requestId } as any }));
    })
  ));

  saveRequest$ = createEffect(() => this.actions$.pipe(
    ofType(RequestActions.saveRequest),
    mergeMap(({ requestDto }) => {
      return this.graphqlService.saveRequest(requestDto)
        .pipe(
          map(response => {
            if (response.status && response.data) {
              return RequestActions.saveRequestSuccess({ request: response.data });
            } else {
              return RequestActions.saveRequestFailure({ error: response.errorDescription || 'Save failed' });
            }
          }),
          catchError(error => of(RequestActions.saveRequestFailure({ error })))
        );
    })
  ));

  constructor(
    private actions$: Actions,
    private graphqlService: GraphqlService
  ) {}
}
