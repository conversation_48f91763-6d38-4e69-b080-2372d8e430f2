import {createReducer, on} from '@ngrx/store';
import {createEntityAdapter, EntityAdapter, EntityState} from '@ngrx/entity';
import {Request} from '../../models/request.model';
import * as RequestActions from './request.actions';

export interface RequestState extends EntityState<Request> {
  selectedRequestId: string | null;
  loading: boolean;
  error: any;
}

export const adapter: EntityAdapter<Request> = createEntityAdapter<Request>({
  selectId: (request: Request) => request.uuid,
  sortComparer: (a: Request, b: Request) => {
    // Sort by date in descending order (newest first)
    return new Date(b.requestedDate).getTime() - new Date(a.requestedDate).getTime();
  },
});

export const initialState: RequestState = adapter.getInitialState({
  selectedRequestId: null,
  loading: false,
  error: null
});

export const requestReducer = createReducer(
  initialState,

  // Load Requests
  on(RequestActions.loadRequests, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RequestActions.loadRequestsSuccess, (state, { requests }) =>
    adapter.setAll(requests, {
      ...state,
      loading: false
    })
  ),

  on(RequestActions.loadRequestsFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Load Request by ID
  on(RequestActions.loadRequestById, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RequestActions.loadRequestByIdSuccess, (state, { request }) =>
    adapter.upsertOne(request, {
      ...state,
      selectedRequestId: request.uuid,
      loading: false
    })
  ),

  on(RequestActions.loadRequestByIdFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Create Request
  on(RequestActions.createRequest, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RequestActions.createRequestSuccess, (state, { request }) =>
    adapter.addOne(request, {
      ...state,
      loading: false
    })
  ),

  on(RequestActions.createRequestFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Update Request
  on(RequestActions.updateRequest, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RequestActions.updateRequestSuccess, (state, { request }) =>
    adapter.updateOne(
      { id: request.uuid, changes: request },
      {
        ...state,
        loading: false
      }
    )
  ),

  on(RequestActions.updateRequestFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Approve Request
  on(RequestActions.approveRequest, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RequestActions.approveRequestSuccess, (state, { request }) =>
    adapter.updateOne(
      { id: request.uuid, changes: request },
      {
        ...state,
        loading: false
      }
    )
  ),

  on(RequestActions.approveRequestFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Reject Request
  on(RequestActions.rejectRequest, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RequestActions.rejectRequestSuccess, (state, { request }) =>
    adapter.updateOne(
      { id: request.uuid, changes: request },
      {
        ...state,
        loading: false
      }
    )
  ),

  on(RequestActions.rejectRequestFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Complete Request
  on(RequestActions.completeRequest, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RequestActions.completeRequestSuccess, (state, { request }) =>
    adapter.updateOne(
      { id: request.uuid, changes: request },
      {
        ...state,
        loading: false
      }
    )
  ),

  on(RequestActions.completeRequestFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  })),

  // Save Request
  on(RequestActions.saveRequest, (state) => ({
    ...state,
    loading: true,
    error: null
  })),

  on(RequestActions.saveRequestSuccess, (state, { request }) =>
    adapter.addOne(request, {
      ...state,
      loading: false
    })
  ),

  on(RequestActions.saveRequestFailure, (state, { error }) => ({
    ...state,
    loading: false,
    error
  }))
);

// Export the entity selectors
export const {
  selectIds,
  selectEntities,
  selectAll,
  selectTotal,
} = adapter.getSelectors();
