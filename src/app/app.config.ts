import {ApplicationConfig, importProviders<PERSON>rom, inject} from "@angular/core";
import {PreloadAllModules, provideRouter, RouteReuseStrategy, withPreloading} from "@angular/router";
import {IonicRouteStrategy, provideIonicAngular} from "@ionic/angular/standalone";
import {routes} from "./app.routes";
import {IonicStorageModule} from "@ionic/storage-angular";
import {NgxPermissionsModule} from "ngx-permissions";
import {provideStore} from "@ngrx/store";
import {requestReducer} from "./store/requests/request.reducer";
import {userReducer} from "./store/users/user.reducer";
import {roleReducer} from "./store/roles/role.reducer";
import {provideEffects} from "@ngrx/effects";
import {RequestEffects} from "./store/requests/request.effects";
import {UserEffects} from "./store/users/user.effects";
import {RoleEffects} from "./store/roles/role.effects";
import {provideStoreDevtools} from "@ngrx/store-devtools";
import {environment} from "../environments/environment";
import {provideApollo} from "apollo-angular";
import {HttpLink} from "apollo-angular/http";
import {ApolloLink, InMemoryCache} from "@apollo/client/core";
import {provideHttpClient, withInterceptors} from "@angular/common/http";
import {onError} from "@apollo/client/link/error";
import { tokenInterceptor } from "./services/token-interceptor/token-interceptor.service";


const errorLink = onError(({ graphQLErrors, networkError }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
      );
    });
  }
  if (networkError) {
    console.error(`[Network error]: ${networkError}`);
  }
});


export const appConfig: ApplicationConfig = {
  providers: [
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    provideIonicAngular(),
    provideRouter(routes, withPreloading(PreloadAllModules)),
    importProvidersFrom(IonicStorageModule.forRoot()),
    importProvidersFrom(NgxPermissionsModule.forRoot()),
    provideStore({
      requests: requestReducer,
      users: userReducer,
      roles: roleReducer
    }),
    provideEffects([RequestEffects, UserEffects, RoleEffects]),
    provideStoreDevtools({
      maxAge: 25,
      logOnly: environment.production,
      autoPause: true,
      trace: false,
      traceLimit: 75,
    }),
    provideApollo(() => {
      const httpLink = inject(HttpLink);

      const http = httpLink.create({
        uri: environment.SERVER_URL + '/graphql' || 'http://localhost:8002/graphql',
      });

      // Create auth link to add authorization header
      const authLink = new ApolloLink((operation, forward) => {
        const token = localStorage.getItem('currentClient');

        operation.setContext({
          headers: {
            authorization: token ? `Bearer ${token}` : '',
          }
        });

        return forward(operation);
      });

      // Chain the links
      const link = ApolloLink.from([authLink, errorLink, http]);

      return {
        link,
        cache: new InMemoryCache(),
        defaultOptions: {
          watchQuery: {
            fetchPolicy: 'network-only',
            errorPolicy: 'all',
          },
          query: {
            fetchPolicy: 'network-only',
            errorPolicy: 'all',
          },
        },
      };
    }),
    provideHttpClient(withInterceptors([tokenInterceptor]))
  ],
}
