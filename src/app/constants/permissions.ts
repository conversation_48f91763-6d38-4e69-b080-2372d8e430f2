/**
 * Application Permissions Constants
 * ================================
 * Centralized permission definitions for the approval flow application.
 * These permissions control access to various features and menu items.
 */

export const PERMISSIONS = {
  // Dashboard Permissions
  ROLE_DASHBOARD_VIEW: 'ROLE_DASHBOARD_VIEW',

  // Role Management Permissions (UAA) - Following ROLE_ENTITY_ACTION pattern
  ROLE_ROLES_VIEW: 'ROLE_ROLES_VIEW',
  ROLE_ROLES_ADD: 'ROLE_ROLES_ADD',
  ROLE_ROLES_EDIT: 'ROLE_ROLES_EDIT',
  ROLE_ROLES_DELETE: 'ROLE_ROLES_DELETE',

  // User Management Permissions (UAA) - Following ROLE_ENTITY_ACTION pattern
  ROLE_USERS_VIEW: 'ROLE_USERS_VIEW',
  ROLE_USERS_ADD: 'ROLE_USERS_ADD',
  ROLE_USERS_EDIT: 'ROLE_USERS_EDIT',
  R<PERSON><PERSON>_USERS_DELETE: 'ROLE_USERS_DELETE',
  R<PERSON><PERSON>_USERS_DEACTIVATE: 'ROLE_USERS_DEACTIVATE',
  ROLE_USERS_ASSIGN_ROLES: 'ROLE_USERS_ASSIGN_ROLES',

  // Request Management Permissions (REQUESTS) - Following ROLE_ENTITY_ACTION pattern
  ROLE_REQUESTS_CREATE: 'ROLE_REQUESTS_CREATE',
  ROLE_REQUESTS_VIEW: 'ROLE_REQUESTS_VIEW',
  ROLE_REQUESTS_VIEW_ALL: 'ROLE_REQUESTS_VIEW_ALL',
  ROLE_REQUESTS_APPROVE: 'ROLE_REQUESTS_APPROVE',
  ROLE_REQUESTS_EDIT: 'ROLE_REQUESTS_EDIT',
  ROLE_REQUESTS_DELETE: 'ROLE_REQUESTS_DELETE',

  // Expense Management Permissions (EXPENSES) - Following ROLE_ENTITY_ACTION pattern
  ROLE_EXPENSES_CREATE: 'ROLE_EXPENSES_CREATE',
  ROLE_EXPENSES_VIEW: 'ROLE_EXPENSES_VIEW',
  ROLE_EXPENSES_VIEW_ALL: 'ROLE_EXPENSES_VIEW_ALL',
  ROLE_EXPENSES_EDIT: 'ROLE_EXPENSES_EDIT',
  ROLE_EXPENSES_DELETE: 'ROLE_EXPENSES_DELETE',
  ROLE_EXPENSES_APPROVE: 'ROLE_EXPENSES_APPROVE',

  // Profile Management Permissions (PROFILE) - Following ROLE_ENTITY_ACTION pattern
  ROLE_PROFILE_VIEW: 'ROLE_PROFILE_VIEW',
  ROLE_PROFILE_EDIT: 'ROLE_PROFILE_EDIT',

  // Settings Management Permissions (SETTINGS) - Following ROLE_ENTITY_ACTION pattern
  ROLE_SETTINGS_VIEW: 'ROLE_SETTINGS_VIEW',
  ROLE_SETTINGS_EDIT: 'ROLE_SETTINGS_EDIT'
} as const;

/**
 * Permission Groups
 * =================
 * Logical groupings of permissions for easier management
 */
export const PERMISSION_GROUPS = {
  // Admin permissions - full access to everything
  ADMIN: [
    PERMISSIONS.ROLE_USERS_VIEW,
    PERMISSIONS.ROLE_USERS_ADD,
    PERMISSIONS.ROLE_USERS_EDIT,
    PERMISSIONS.ROLE_USERS_DELETE,
    PERMISSIONS.ROLE_USERS_DEACTIVATE,
    PERMISSIONS.ROLE_USERS_ASSIGN_ROLES,
    PERMISSIONS.ROLE_ROLES_VIEW,
    PERMISSIONS.ROLE_ROLES_ADD,
    PERMISSIONS.ROLE_ROLES_EDIT,
    PERMISSIONS.ROLE_ROLES_DELETE,
    PERMISSIONS.ROLE_SETTINGS_VIEW,
    PERMISSIONS.ROLE_SETTINGS_EDIT,
    PERMISSIONS.ROLE_DASHBOARD_VIEW
  ],

  // Manager permissions - can manage requests and approvals
  MANAGER: [
    PERMISSIONS.ROLE_REQUESTS_VIEW_ALL,
    PERMISSIONS.ROLE_REQUESTS_APPROVE,
    PERMISSIONS.ROLE_EXPENSES_VIEW_ALL,
    PERMISSIONS.ROLE_EXPENSES_APPROVE,
    PERMISSIONS.ROLE_DASHBOARD_VIEW
  ],

  // User permissions - basic access
  USER: [
    PERMISSIONS.ROLE_REQUESTS_CREATE,
    PERMISSIONS.ROLE_REQUESTS_VIEW,
    PERMISSIONS.ROLE_REQUESTS_EDIT,
    PERMISSIONS.ROLE_EXPENSES_CREATE,
    PERMISSIONS.ROLE_EXPENSES_VIEW,
    PERMISSIONS.ROLE_PROFILE_VIEW,
    PERMISSIONS.ROLE_PROFILE_EDIT,
    PERMISSIONS.ROLE_DASHBOARD_VIEW
  ]
} as const;

/**
 * Menu Item Permission Requirements
 * =================================
 * Defines which permissions are required for each menu item
 */
export const MENU_PERMISSIONS = {
  DASHBOARD: [PERMISSIONS.ROLE_DASHBOARD_VIEW],
  REQUESTS: [PERMISSIONS.ROLE_REQUESTS_VIEW, PERMISSIONS.ROLE_REQUESTS_CREATE],
  APPROVALS: [PERMISSIONS.ROLE_REQUESTS_APPROVE, PERMISSIONS.ROLE_REQUESTS_VIEW_ALL],
  USERS: [PERMISSIONS.ROLE_USERS_VIEW, PERMISSIONS.ROLE_USERS_ADD],
  ROLES: [PERMISSIONS.ROLE_ROLES_VIEW, PERMISSIONS.ROLE_ROLES_ADD],
  EXPENSES: [PERMISSIONS.ROLE_EXPENSES_VIEW, PERMISSIONS.ROLE_EXPENSES_CREATE],
  PROFILE: [PERMISSIONS.ROLE_PROFILE_VIEW],
  SETTINGS: [PERMISSIONS.ROLE_SETTINGS_VIEW, PERMISSIONS.ROLE_SETTINGS_EDIT]
};

/**
 * Helper function to check if user has any of the required permissions
 */
export function hasAnyPermission(userPermissions: string[], requiredPermissions: string[]): boolean {
  return requiredPermissions.some(permission => userPermissions.includes(permission));
}

/**
 * Helper function to check if user has all required permissions
 */
export function hasAllPermissions(userPermissions: string[], requiredPermissions: string[]): boolean {
  return requiredPermissions.every(permission => userPermissions.includes(permission));
}
