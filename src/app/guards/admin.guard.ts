import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { NgxPermissionsService } from 'ngx-permissions';
import { AuthService } from '../services/auth/auth.service';
import { PermissionInitService } from '../services/permission-init.service';
import { PERMISSIONS } from '../constants/permissions';

export const adminGuard: CanActivateFn = async (_route, _state) => {
  const permissionsService = inject(NgxPermissionsService);
  const authService = inject(AuthService);
  const router = inject(Router);
  const permissionInitService = inject(PermissionInitService);

  try {
    // Ensure permissions are initialized
    await permissionInitService.initializePermissions();

    // Ensure user is authenticated first
    if (!authService.isAuthenticated()) {
      router.navigate(['/login']);
      return false;
    }

    // Ensure permissions are loaded
    if (!authService.hasPermissionsLoaded()) {
      const permissionsLoaded = await authService.refreshPermissions();
      if (!permissionsLoaded) {
        router.navigate(['/login']);
        return false;
      }
    }

    // Check if user has admin permissions
    const permissions = permissionsService.getPermissions();
    const hasAdminPermission = permissions[PERMISSIONS.ROLE_USERS_VIEW] ||
                              permissions[PERMISSIONS.ROLE_USERS_ADD] ||
                              permissions[PERMISSIONS.ROLE_ROLES_VIEW];

    if (hasAdminPermission) {
      return true;
    } else {
      // Redirect to dashboard if not admin
      router.navigate(['/menu/dashboard']);
      return false;
    }
  } catch (error) {
    console.error('Error in admin guard:', error);
    router.navigate(['/login']);
    return false;
  }
};
