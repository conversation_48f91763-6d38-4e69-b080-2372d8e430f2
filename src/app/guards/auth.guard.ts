import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { AuthService } from '../services/auth/auth.service'; // Adjust path as needed
import { PermissionInitService } from '../services/permission-init.service';

export const authGuard: CanActivateFn = async (route, state) => { // Make it async
  const authService = inject(AuthService);
  const router = inject(Router);
  const permissionInitService = inject(PermissionInitService);

  // Await the initialization of authStatus to ensure it's up-to-date
  // This assumes authService.initializeAuthStatus() has been called at app startup
  // or that authStatus signal is reliable after app init.
  // To be absolutely sure, we can call initializeAuthStatus here if needed,
  // or rely on the fact that it's called in AuthService constructor.
  // For this guard, we'll assume authStatus is correctly initialized.
  // If not, we might need to ensure `initializeAuthStatus` completes.
  // One way is to make `AuthService.authStatus` a promise or observable that resolves after init.
  // However, with signals, `authService.isAuthenticated()` should return the current signal value.
  // The `initializeAuthStatus` updates this signal.

  // The `isAuthenticated()` in our AuthService now reads from a signal, which is synchronous.
  // The signal itself is updated asynchronously by `initializeAuthStatus()`.
  // If the guard runs before `initializeAuthStatus` completes, it might get a stale value.
  // To ensure the guard has the latest status, especially on first load:
  // Option A: Make `AuthService.isAuthenticated()` async and internally await `initializeAuthStatus` if not yet done. (Complex for existing structure)
  // Option B: Have `AuthService.initializeAuthStatus()` return a promise, and await it here or in a resolver.
  // Option C: Rely on `initializeAuthStatus` in `AuthService` constructor and assume it completes or signal updates before routing.
  // Given the current AuthService, `isAuthenticated()` reads the signal, which is fine.
  // The concern is whether the signal has been updated from async storage yet.
  // A simple fix is to ensure `initializeAuthStatus` is awaited somewhere central, or make the guard robust.

  // Ensure permissions are initialized before allowing route access
  try {
    // Initialize permissions if not already done
    await permissionInitService.initializePermissions();

    // Check authentication status
    if (authService.isAuthenticated()) {
      // Ensure permissions are loaded for authenticated users
      if (!authService.hasPermissionsLoaded()) {
        console.warn('User is authenticated but permissions not loaded, attempting refresh...');
        const permissionsLoaded = await authService.refreshPermissions();
        if (!permissionsLoaded) {
          console.error('Failed to load permissions, redirecting to login');
          router.navigate(['/login']);
          return false;
        }
      }
      return true;
    } else {
      // Redirect to the login page
      router.navigate(['/login']);
      return false;
    }
  } catch (error) {
    console.error('Error in auth guard:', error);
    router.navigate(['/login']);
    return false;
  }
};
