// Custom styles for expense form page using CSS parts and shadow DOM

// Override Ionic header and toolbar
:host {
  --ion-background-color: #f5f7fa;
  --ion-toolbar-background: transparent;
  --ion-toolbar-border-color: transparent;
  --ion-header-background: transparent;
  --ion-item-background: transparent;

  // Typography
  --ion-font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;

  // Card styles
  --ion-card-background: #ffffff;
  --ion-card-border-radius: 12px;
  --ion-card-margin: 8px;
  --ion-card-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

  // Button styles
  --ion-color-primary: #3880ff;
  --ion-color-primary-contrast: #ffffff;
}

// Custom header styling
::part(header) {
  box-shadow: none;
  border-bottom: none;
  background: transparent;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
}

::part(toolbar) {
  --min-height: 56px;
  --background: transparent;
  --border-color: transparent;
}

::part(title) {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

// Content styling
ion-content {
  --background: linear-gradient(135deg, #f5f7fa 0%, #eef2f6 100%);
  --padding-top: 56px;
}

// Card styling
ion-card {
  margin: 16px 0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid rgba(0, 0, 0, 0.03);
  overflow: hidden;
  backdrop-filter: blur(8px);

  &::part(native) {
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.9);
  }
}

ion-card-header {
  padding: 16px 16px 0;

  &::part(native) {
    padding: 0;
  }
}

ion-card-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #374151;

  &::part(native) {
    font-size: 18px;
    font-weight: 600;
  }

  ion-icon {
    font-size: 20px;
    margin-right: 8px;
    vertical-align: middle;
    color: var(--ion-color-primary);
  }
}

ion-card-content {
  padding: 16px;

  &::part(native) {
    padding: 16px;
  }
}

// Section titles
.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 16px 0 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

// Form styling
ion-item {
  --background: rgba(255, 255, 255, 0.5);
  --border-color: rgba(0, 0, 0, 0.08);
  --border-radius: 8px;
  --padding-start: 12px;
  margin-bottom: 8px;

  &::part(native) {
    border-radius: 8px;
  }

  ion-label {
    color: #4b5563;
    font-weight: 500;
  }

  ion-input, ion-select, ion-textarea {
    --color: #1f2937;
    font-weight: 500;
  }
}

// Error message styling
.error-message {
  padding: 0 16px;
  margin-top: -4px;
  margin-bottom: 12px;
  font-size: 12px;
  display: flex;
  align-items: center;

  ion-icon {
    margin-right: 4px;
  }
}

// Expenditure card styling
.expenditure-card {
  margin: 8px 0;
  border-left: 3px solid var(--ion-color-primary);
  background-color: rgba(255, 255, 255, 0.7);
}

// Expenditure actions
.expenditure-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

// Form actions
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

// Button styling
ion-button {
  --border-radius: 8px;
  --box-shadow: none;
  font-weight: 500;
  text-transform: none;

  &::part(native) {
    font-weight: 500;
    text-transform: none;
  }
}

// Responsive adjustments
@media (min-width: 768px) {
  ion-content {
    --padding-top: 72px;
  }
}
