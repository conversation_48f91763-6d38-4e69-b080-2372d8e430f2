import {Component, OnInit, ViewEncapsulation} from '@angular/core';
import {CommonModule} from '@angular/common';
import {
  AbstractControl,
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import {Router} from '@angular/router';
import {
  IonBackButton,
  IonButton,
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonCol,
  IonContent,
  IonGrid,
  IonHeader,
  IonIcon,
  IonInput,
  IonItem,
  IonLabel,
  IonMenuButton,
  IonRow,
  IonSelect,
  IonSelectOption,
  IonText,
  IonTextarea,
  IonTitle,
  IonToolbar
} from '@ionic/angular/standalone';
import {addIcons} from 'ionicons';
import {
  addOutline,
  calendarOutline,
  closeOutline,
  saveOutline,
  trashOutline
} from 'ionicons/icons';
import {BehaviorSubject, Observable, of} from 'rxjs';

// Mock data for the form
interface ExpenseType {
  id: number;
  name: string;
}

interface Shift {
  id: number;
  shiftName: string;
}

interface Account {
  id: number;
  accountable: {
    accountName: string;
  };
  accountTypeId: number;
}

interface AccountCategory {
  id: number;
  name: string;
}

// Form interfaces
interface ExpenditureFormGroup {
  expenseDate: FormControl<any>;
  shiftId: FormControl<any>;
  expenditures: FormArray<FormGroup<ExpenditureItemFormGroup>>;
}

interface ExpenditureItemFormGroup {
  expenseTypeId: FormControl<any>;
  amount: FormControl<any>;
  accountId: FormControl<any>;
  notes: FormControl<any>;
}

@Component({
  selector: 'app-expense-form',
  templateUrl: './expense-form.page.html',
  styleUrls: ['./expense-form.page.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonButton,
    IonButtons,
    IonBackButton,
    IonMenuButton,
    IonIcon,
    IonItem,
    IonLabel,
    IonInput,
    IonSelect,
    IonSelectOption,
    IonTextarea,
    IonText,
    IonGrid,
    IonRow,
    IonCol
  ]
})
export class ExpenseFormPage implements OnInit {
  title = 'Add Expenditure';
  expenditureForm!: FormGroup<ExpenditureFormGroup>;
  expenditure: any = null; // For edit mode
  saveLoading$ = new BehaviorSubject<boolean>(false);

  // Mock data observables
  shifts$: Observable<Shift[]>;
  expenseType$: Observable<ExpenseType[]>;

  // Account categories and accounts
  accountCategories: AccountCategory[] = [
    { id: 1, name: 'Cash' },
    { id: 2, name: 'Bank' },
    { id: 3, name: 'Mobile Money' }
  ];

  accounts: Account[] = [
    { id: 1, accountable: { accountName: 'Cash Account' }, accountTypeId: 1 },
    { id: 2, accountable: { accountName: 'Bank Account 1' }, accountTypeId: 2 },
    { id: 3, accountable: { accountName: 'Bank Account 2' }, accountTypeId: 2 },
    { id: 4, accountable: { accountName: 'Mobile Money 1' }, accountTypeId: 3 },
    { id: 5, accountable: { accountName: 'Mobile Money 2' }, accountTypeId: 3 }
  ];

  // Track selected account types for each expenditure field
  selectedAccountTypes: number[] = [];

  constructor(private formBuilder: FormBuilder, private router: Router) {
    addIcons({
      'calendar': calendarOutline,
      'add': addOutline,
      'close': closeOutline,
      'trash': trashOutline,
      'save': saveOutline
    });

    // Initialize mock data
    this.shifts$ = of([
      { id: 1, shiftName: 'Morning Shift' },
      { id: 2, shiftName: 'Afternoon Shift' },
      { id: 3, shiftName: 'Evening Shift' }
    ]);

    this.expenseType$ = of([
      { id: 1, name: 'Fuel' },
      { id: 2, name: 'Maintenance' },
      { id: 3, name: 'Salary' },
      { id: 4, name: 'Utilities' },
      { id: 5, name: 'Other' }
    ]);
  }

  ngOnInit() {
    this.initForm();
  }

  initForm() {
    const today = new Date();

    this.expenditureForm = this.formBuilder.group({
      expenseDate: this.formBuilder.control<any>(today.toISOString().split('T')[0], { validators: Validators.required }),
      shiftId: this.formBuilder.control<any>('', { validators: Validators.required }),
      expenditures: this.formBuilder.array<FormGroup<ExpenditureItemFormGroup>>([])
    }) as FormGroup<ExpenditureFormGroup>;

    // Add initial expenditure field
    this.addExpenditureField(null);
  }

  // Getter for expenditures form array
  get expendituresFiled(): FormArray<FormGroup<ExpenditureItemFormGroup>> {
    return this.expenditureForm.get('expenditures') as FormArray<FormGroup<ExpenditureItemFormGroup>>;
  }

  // Create a new expenditure form group
  createExpenditureField(data: any = null): FormGroup<ExpenditureItemFormGroup> {
    return this.formBuilder.group({
      expenseTypeId: this.formBuilder.control<any>(data?.expenseTypeId || '', { validators: Validators.required }),
      amount: this.formBuilder.control<any>(data?.amount || '', { validators: [Validators.required, Validators.min(0)] }),
      accountId: this.formBuilder.control<any>(data?.accountId || '', { validators: Validators.required }),
      notes: this.formBuilder.control<any>(data?.notes || '', { validators: Validators.required })
    }) as FormGroup<ExpenditureItemFormGroup>;
  }

  // Add a new expenditure field
  addExpenditureField(data: any) {
    this.expendituresFiled.push(this.createExpenditureField(data));
    this.selectedAccountTypes.push(null);
  }

  // Remove an expenditure field
  removeField(index: number) {
    this.expendituresFiled.removeAt(index);
    this.selectedAccountTypes.splice(index, 1);
  }

  // Handle account type change
  onAccountTypeChanged(event: any, index: number) {
    this.selectedAccountTypes[index] = event;
    // Reset the account selection when account type changes
    const control = this.expendituresFiled.at(index);
    control.get('accountId')?.setValue('');
  }

  // Get accounts filtered by selected account type
  getAccountsByType(index: number): Account[] {
    const selectedType = this.selectedAccountTypes[index];
    if (!selectedType) return this.accounts;
    return this.accounts.filter(account => account.accountTypeId === selectedType);
  }

  // Handle date change
  onDateChange(event: any) {
    console.log('Date changed:', event);
  }

  // Form submission
  save(formValue: any) {
    if (this.expenditureForm.valid) {
      this.saveLoading$.next(true);

      // Simulate API call
      setTimeout(() => {
        console.log('Form submitted:', formValue);
        this.saveLoading$.next(false);
        this.router.navigate(['/menu/dashboard']);
      }, 1500);
    }
  }

  // Close the form
  closeModel() {
    this.router.navigate(['/menu/dashboard']);
  }
}
