<ion-header [translucent]="true" class="ion-no-border">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
      <ion-back-button defaultHref="/menu/dashboard"></ion-back-button>
    </ion-buttons>
    <ion-title>{{ title }}</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <ion-card>
    <ion-card-header>
      <ion-card-title>
        <ion-icon name="save" class="mr-2"></ion-icon>
        {{ title }}
      </ion-card-title>
    </ion-card-header>

    <ion-card-content>
      <form [formGroup]="expenditureForm" (ngSubmit)="save(expenditureForm.value)">
        <ion-grid>
          <ion-row>
            <ion-col size="12">
              <h5 class="section-title">Shift Details</h5>
            </ion-col>

            <ion-col size="12" size-md="6">
              <ion-item [class.ion-invalid]="!expenditureForm.get('expenseDate')?.valid && expenditureForm.get('expenseDate')?.touched">
                <ion-label position="floating">Shift Date</ion-label>
                <ion-input type="date" formControlName="expenseDate" (ionChange)="onDateChange($event)"></ion-input>
                <ion-icon name="calendar" slot="end"></ion-icon>
              </ion-item>
              <ion-text color="danger" *ngIf="!expenditureForm.get('expenseDate')?.valid && expenditureForm.get('expenseDate')?.touched && expenditureForm.get('expenseDate')?.errors?.['required']" class="error-message">
                <ion-icon name="close"></ion-icon> The date field is required.
              </ion-text>
            </ion-col>

            <ion-col size="12" size-md="6">
              <ion-item [class.ion-invalid]="!expenditureForm.get('shiftId')?.valid && expenditureForm.get('shiftId')?.touched">
                <ion-label position="floating">Shift</ion-label>
                <ion-select formControlName="shiftId" interface="popover">
                  <ion-select-option *ngFor="let shift of shifts$ | async" [value]="shift.id">{{ shift.shiftName }}</ion-select-option>
                </ion-select>
              </ion-item>
              <ion-text color="danger" *ngIf="!expenditureForm.get('shiftId')?.valid && expenditureForm.get('shiftId')?.touched && expenditureForm.get('shiftId')?.errors?.['required']" class="error-message">
                <ion-icon name="close"></ion-icon> The shift field is required.
              </ion-text>
            </ion-col>

            <ion-col size="12">
              <h5 class="section-title">Expenditures</h5>
            </ion-col>

            <ion-col size="12" *ngFor="let control of expendituresFiled.controls; let i = index">
              <ion-card class="expenditure-card" [formGroup]="control">
                <ion-card-content>
                  <ion-grid>
                    <ion-row>
                      <ion-col size="12" size-md="6">
                        <ion-item [class.ion-invalid]="!control.get('expenseTypeId')?.valid && control.get('expenseTypeId')?.touched">
                          <ion-label position="floating">Expense Type</ion-label>
                          <ion-select formControlName="expenseTypeId" interface="popover">
                            <ion-select-option *ngFor="let type of expenseType$ | async" [value]="type.id">{{ type.name }}</ion-select-option>
                          </ion-select>
                        </ion-item>
                        <ion-text color="danger" *ngIf="!control.get('expenseTypeId')?.valid && control.get('expenseTypeId')?.touched && control.get('expenseTypeId')?.errors?.['required']" class="error-message">
                          <ion-icon name="close"></ion-icon> The expense type field is required.
                        </ion-text>
                      </ion-col>

                      <ion-col size="12" size-md="6">
                        <ion-item [class.ion-invalid]="!control.get('amount')?.valid && control.get('amount')?.touched">
                          <ion-label position="floating">Amount</ion-label>
                          <ion-input type="number" min="0" formControlName="amount"></ion-input>
                        </ion-item>
                        <ion-text color="danger" *ngIf="!control.get('amount')?.valid && control.get('amount')?.touched && control.get('amount')?.errors?.['required']" class="error-message">
                          <ion-icon name="close"></ion-icon> The amount field is required.
                        </ion-text>
                      </ion-col>

                      <ion-col size="12" size-md="6" *ngIf="!expenditure">
                        <ion-item>
                          <ion-label position="floating">Account Type Used</ion-label>
                          <ion-select (ionChange)="onAccountTypeChanged($event.detail.value, i)" interface="popover">
                            <ion-select-option *ngFor="let category of accountCategories" [value]="category.id">{{ category.name }}</ion-select-option>
                          </ion-select>
                        </ion-item>
                      </ion-col>

                      <ion-col size="12" size-md="6">
                        <ion-item [class.ion-invalid]="!control.get('accountId')?.valid && control.get('accountId')?.touched">
                          <ion-label position="floating">Account Used</ion-label>
                          <ion-select formControlName="accountId" interface="popover">
                            <ion-select-option *ngFor="let account of getAccountsByType(i)" [value]="account.id">{{ account.accountable.accountName }}</ion-select-option>
                          </ion-select>
                        </ion-item>
                        <ion-text color="danger" *ngIf="!control.get('accountId')?.valid && control.get('accountId')?.touched && control.get('accountId')?.errors?.['required']" class="error-message">
                          <ion-icon name="close"></ion-icon> Collection Account is required field.
                        </ion-text>
                      </ion-col>

                      <ion-col size="12">
                        <ion-item [class.ion-invalid]="!control.get('notes')?.valid && control.get('notes')?.touched">
                          <ion-label position="floating">Notes</ion-label>
                          <ion-textarea rows="3" formControlName="notes"></ion-textarea>
                        </ion-item>
                        <ion-text color="danger" *ngIf="!control.get('notes')?.valid && control.get('notes')?.touched && control.get('notes')?.errors?.['required']" class="error-message">
                          <ion-icon name="close"></ion-icon> Notes field is required.
                        </ion-text>
                      </ion-col>
                    </ion-row>
                  </ion-grid>

                  <div class="expenditure-actions" *ngIf="!expenditure">
                    <ion-button fill="outline" color="danger" *ngIf="((i === 0 && expendituresFiled.controls.length > 1) || (i > 0))" (click)="removeField(i)">
                      <ion-icon name="trash" slot="start"></ion-icon>
                      Remove
                    </ion-button>

                    <ion-button fill="outline" color="primary" *ngIf="((i === 0 && expendituresFiled.controls.length === 1) || i === expendituresFiled.controls.length - 1) && i < 5" (click)="addExpenditureField(null)">
                      <ion-icon name="add" slot="start"></ion-icon>
                      Add Another
                    </ion-button>
                  </div>
                </ion-card-content>
              </ion-card>
            </ion-col>
          </ion-row>
        </ion-grid>

        <div class="form-actions">
          <ion-button fill="outline" (click)="closeModel()">
            Cancel
          </ion-button>
          <ion-button type="submit" [disabled]="!expenditureForm.valid || (saveLoading$ | async)">
            <ion-icon name="save" slot="start"></ion-icon>
            {{ (saveLoading$ | async) ? 'Saving...' : 'Submit' }}
          </ion-button>
        </div>
      </form>
    </ion-card-content>
  </ion-card>
</ion-content>
