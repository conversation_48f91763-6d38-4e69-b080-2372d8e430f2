import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { LoginPage } from './login.page';
import { AuthService } from '../../services/auth/auth.service';
import { NotificationService } from '../../services/notification.service';

describe('LoginPage', () => {
  let component: LoginPage;
  let fixture: ComponentFixture<LoginPage>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockNotificationService: jasmine.SpyObj<NotificationService>;
  let mockRouter: jasmine.SpyObj<Router>;

  beforeEach(async () => {
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['login', 'me']);
    const notificationServiceSpy = jasmine.createSpyObj('NotificationService', ['showSuccess', 'showError']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [LoginPage, ReactiveFormsModule],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: NotificationService, useValue: notificationServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LoginPage);
    component = fixture.componentInstance;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockNotificationService = TestBed.inject(NotificationService) as jasmine.SpyObj<NotificationService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    expect(component.loginForm.get('username')?.value).toBe('');
    expect(component.loginForm.get('password')?.value).toBe('');
  });

  it('should toggle password visibility', () => {
    expect(component.showPassword).toBeFalse();
    component.togglePassword();
    expect(component.showPassword).toBeTrue();
    component.togglePassword();
    expect(component.showPassword).toBeFalse();
  });

  it('should validate required fields', () => {
    component.onSubmit();
    expect(component.isSubmitted).toBeTrue();
    expect(component.loginForm.invalid).toBeTrue();
    expect(mockNotificationService.showError).toHaveBeenCalledWith('Please fill in all required fields correctly.');
  });

  it('should call auth service on valid form submission', async () => {
    component.loginForm.patchValue({
      username: 'testuser',
      password: 'password123'
    });

    mockAuthService.login.and.returnValue(Promise.resolve({ token: 'fake-token' }));
    mockAuthService.me.and.returnValue(Promise.resolve());

    await component.onSubmit();

    expect(mockAuthService.login).toHaveBeenCalledWith({
      username: 'testuser',
      password: 'password123'
    });
    expect(mockAuthService.me).toHaveBeenCalled();
    expect(mockNotificationService.showSuccess).toHaveBeenCalledWith('Login successful!');
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/menu/dashboard']);
  });

  it('should ensure login method stores token before calling me()', async () => {
    component.loginForm.patchValue({
      username: 'testuser',
      password: 'password123'
    });

    // Mock the login to return a token
    mockAuthService.login.and.returnValue(Promise.resolve({ token: 'fake-token' }));
    mockAuthService.me.and.returnValue(Promise.resolve());

    await component.onSubmit();

    // Verify that login is called before me()
    expect(mockAuthService.login).toHaveBeenCalledBefore(mockAuthService.me as jasmine.Spy);
  });

  it('should handle login error', async () => {
    component.loginForm.patchValue({
      username: 'testuser',
      password: 'wrongpassword'
    });

    mockAuthService.login.and.returnValue(Promise.reject({ message: 'Invalid credentials' }));

    await component.onSubmit();

    expect(mockNotificationService.showError).toHaveBeenCalledWith('Login failed: Invalid credentials');
    expect(component.isLoading).toBeFalse();
  });
});
