<ion-content [fullscreen]="true" class="app-page has-gradient-bg">
  <!-- Background Elements -->
  <div class="app-background">
    <div class="app-floating-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>

  <!-- Pull to refresh -->
  <ion-refresher slot="fixed" (ionRefresh)="handleRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <!-- Main Requests Container -->
  <div class="app-container">
    <div class="app-content app-content-wide">
      <!-- Header Section -->
      <div class="app-header">
        <div class="header-left">
          <ion-button fill="clear" class="app-header-button">
            <ion-menu-button slot="icon-only"></ion-menu-button>
          </ion-button>
          <h1 class="header-title">Access Requests</h1>
        </div>
        <div class="header-right">
          <ion-button
            *ngxPermissionsOnly="['ROLE_REQUESTS_CREATE']"
            fill="clear"
            class="app-header-button"
            (click)="addNewRequest()">
            <ion-icon name="add-outline" slot="icon-only"></ion-icon>
          </ion-button>
          <ion-button fill="clear" class="app-header-button">
            <ion-icon name="funnel-outline" slot="icon-only"></ion-icon>
          </ion-button>
        </div>
      </div>

      <!-- Search and Filter Section -->
      <div class="app-card search-filter-card mb-lg">
        <div class="search-filter-header mb-md">
          <h3 class="text-lg font-semibold text-gray-800 m-0">Filter Requests</h3>
          <p class="text-sm text-gray-600 m-0 mt-xs">Search and filter access requests</p>
        </div>

        <div class="search-filter-content">
          <!-- Search Input -->
          <div class="app-form-group">
            <label class="app-form-label">Search</label>
            <div class="app-input-container">
              <ion-icon name="search-outline" class="app-input-icon"></ion-icon>
              <ion-input
                type="text"
                placeholder="Search by ID, requester, facility, or purpose..."
                [value]="searchTerm"
                (ionInput)="onSearchChange($event)"
                class="app-input">
              </ion-input>
              <button *ngIf="searchTerm" type="button" class="app-input-action" (click)="clearSearch()">
                <ion-icon name="close-outline"></ion-icon>
              </button>
            </div>
          </div>

          <!-- Status Filter -->
          <div class="app-form-group">
            <label class="app-form-label">Status Filter</label>
            <div class="app-input-container">
              <ion-icon name="funnel-outline" class="app-input-icon"></ion-icon>
              <ion-select
                interface="popover"
                placeholder="All Status"
                [value]="statusFilter"
                (ionChange)="onStatusFilterChange($event)"
                class="app-input">
                <ion-select-option value="all">All Status</ion-select-option>
                <ion-select-option value="pending">Pending</ion-select-option>
                <ion-select-option value="approved">Approved</ion-select-option>
                <ion-select-option value="rejected">Rejected</ion-select-option>
                <ion-select-option value="completed">Completed</ion-select-option>
              </ion-select>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading$ | async" class="app-card text-center">
        <div class="loading-content">
          <ion-spinner name="crescent" class="loading-spinner"></ion-spinner>
          <h3 class="text-lg font-semibold text-gray-800 mt-md mb-xs">Loading Requests</h3>
          <p class="text-gray-600 text-sm m-0">Please wait while we fetch your access requests...</p>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="error$ | async as error" class="app-card text-center">
        <div class="error-content">
          <div class="error-icon mb-md">
            <ion-icon name="alert-circle-outline" class="text-danger"></ion-icon>
          </div>
          <h3 class="text-lg font-semibold text-gray-800 mb-xs">Error Loading Requests</h3>
          <p class="text-gray-600 text-sm mb-lg">{{ error?.message || 'Please try again later' }}</p>
          <ion-button class="app-button app-button-outline" (click)="handleRefresh($event)">
            <div class="app-button-content">
              <ion-icon name="refresh-outline" class="app-button-icon"></ion-icon>
              <span>Retry</span>
            </div>
          </ion-button>
        </div>
      </div>

      <!-- Requests List -->
      <div class="requests-container" *ngIf="!(loading$ | async) && !(error$ | async)">
        <!-- No Results Message -->
        <div *ngIf="filteredRequests.length === 0" class="app-card text-center">
          <div class="no-results-content">
            <div class="no-results-icon mb-md">
              <ion-icon name="document-text-outline" class="text-gray-400"></ion-icon>
            </div>
            <h3 class="text-lg font-semibold text-gray-800 mb-xs">No Requests Found</h3>
            <p class="text-gray-600 text-sm mb-lg">Try adjusting your search terms or filters</p>
            <ion-button class="app-button app-button-outline" (click)="clearFilters()">
              <div class="app-button-content">
                <ion-icon name="refresh-outline" class="app-button-icon"></ion-icon>
                <span>Clear Filters</span>
              </div>
            </ion-button>
          </div>
        </div>

        <!-- Request Cards -->
        <div class="requests-grid">
          <div *ngFor="let request of filteredRequests" class="app-card request-card" (click)="viewRequestDetails(request)">
            <!-- Request Header -->
            <div class="request-header mb-md">
              <div class="request-id-section">
                <h4 class="request-title">{{ request.title }}</h4>
                <p class="request-date text-gray-500 text-sm m-0">{{ formatDate(request.requestedDate) }}</p>
              </div>
              <div class="app-badge" [ngClass]="getStatusBadgeClass(request.status)">
                {{ request.status }}
              </div>
            </div>

            <!-- Request Details -->
            <div class="request-details mb-lg">
              <!-- Description -->
              <div class="app-list-item compact">
                <div class="app-list-icon small">
                  <ion-icon name="document-text-outline"></ion-icon>
                </div>
                <div class="app-list-content">
                  <div class="app-list-title">Description</div>
                  <div class="app-list-subtitle">{{ request.description }}</div>
                </div>
              </div>
            </div>

            <!-- Request Actions -->
            <div class="request-actions">
              <ion-button
                fill="clear"
                class="details-button"
                (click)="viewRequestDetails(request); $event.stopPropagation()">
                <span>View Details</span>
                <ion-icon name="chevron-forward-outline" slot="end"></ion-icon>
              </ion-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Request Creation Modal -->
  <ion-modal [isOpen]="isModalOpen" (didDismiss)="closeModal()">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-button (click)="closeModal()">Cancel</ion-button>
          </ion-buttons>
          <ion-title>Create New Request</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="saveRequest()" [strong]="true" [disabled]="requestForm.invalid || (loading$ | async)">
              {{ (loading$ | async) ? 'Creating...' : 'Create' }}
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-content>
        <form [formGroup]="requestForm" class="app-form">
          <!-- Title Field -->
          <ion-item class="app-form-item">
            <ion-label position="stacked">Title *</ion-label>
            <ion-input
              formControlName="title"
              placeholder="Enter request title"
              maxlength="100"
              [class.ion-invalid]="requestForm.get('title')?.invalid && requestForm.get('title')?.touched">
            </ion-input>
          </ion-item>
          <div *ngIf="requestForm.get('title')?.invalid && requestForm.get('title')?.touched" class="app-form-error">
            <span *ngIf="requestForm.get('title')?.errors?.['required']">Title is required</span>
            <span *ngIf="requestForm.get('title')?.errors?.['maxlength']">Title must be less than 100 characters</span>
          </div>

          <!-- Description Field -->
          <ion-item class="app-form-item">
            <ion-label position="stacked">Description *</ion-label>
            <ion-textarea
              formControlName="description"
              placeholder="Enter request description"
              rows="4"
              maxlength="500"
              [class.ion-invalid]="requestForm.get('description')?.invalid && requestForm.get('description')?.touched">
            </ion-textarea>
          </ion-item>
          <div *ngIf="requestForm.get('description')?.invalid && requestForm.get('description')?.touched" class="app-form-error">
            <span *ngIf="requestForm.get('description')?.errors?.['required']">Description is required</span>
            <span *ngIf="requestForm.get('description')?.errors?.['maxlength']">Description must be less than 500 characters</span>
          </div>
        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
