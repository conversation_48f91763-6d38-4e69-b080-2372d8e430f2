import {Component, OnInit, ViewEncapsulation} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {RouterLink} from '@angular/router';
import {
  IonButton,
  IonContent,
  IonIcon,
  IonMenuButton,
} from '@ionic/angular/standalone';
import {addIcons} from 'ionicons';
import {
  alertCircleOutline,
  checkmarkCircleOutline,
  chevronForwardOutline,
  documentTextOutline,
  listOutline,
  menuOutline,
  notificationsOutline,
  peopleOutline,
  settingsOutline,
  statsChartOutline,
  timeOutline
} from 'ionicons/icons';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.page.html',
  styleUrls: ['./dashboard.page.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    IonContent,
    IonButton,
    IonIcon,
    IonMenuButton,
    CommonModule,
    FormsModule,
    RouterLink
  ]
})
export class DashboardPage implements OnInit {
  // Mock data for dashboard
  stats = {
    pendingRequests: 12,
    approvedRequests: 45,
    rejectedRequests: 8,
    totalUsers: 24
  };

  recentRequests = [
    { id: 1, technician: 'John Doe', asset: 'Server Room A', status: 'Pending', date: '2023-05-01' },
    { id: 2, technician: 'Jane Smith', asset: 'Network Switch B', status: 'Approved', date: '2023-04-28' },
    { id: 3, technician: 'Mike Johnson', asset: 'Database Server', status: 'Rejected', date: '2023-04-25' }
  ];

  constructor() {
    addIcons({
      'document-text': documentTextOutline,
      'checkmark-circle': checkmarkCircleOutline,
      'checkmark-circle-outline': checkmarkCircleOutline,
      'close-circle-outline': alertCircleOutline,
      'people': peopleOutline,
      'people-outline': peopleOutline,
      'menu': menuOutline,
      'stats-chart': statsChartOutline,
      'time': timeOutline,
      'time-outline': timeOutline,
      'alert-circle': alertCircleOutline,
      'chevron-forward-outline': chevronForwardOutline,
      'list-outline': listOutline,
      'notifications-outline': notificationsOutline,
      'settings-outline': settingsOutline
    });
  }

  ngOnInit() {
  }

  getRequestIcon(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'time-outline';
      case 'approved':
        return 'checkmark-circle-outline';
      case 'rejected':
        return 'close-circle-outline';
      default:
        return 'document-text-outline';
    }
  }

  getRequestIconClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'text-warning';
      case 'approved':
        return 'text-success';
      case 'rejected':
        return 'text-danger';
      default:
        return 'text-gray-500';
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'app-badge-warning';
      case 'approved':
        return 'app-badge-success';
      case 'rejected':
        return 'app-badge-danger';
      default:
        return 'app-badge-gray';
    }
  }

}
