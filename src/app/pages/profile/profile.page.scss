// Profile Page Specific Styles
// =============================================================================
// All common styles are now in the global theme system
// This file only contains profile-specific customizations

:host {
  // Override any Ionic defaults if needed
  --ion-background-color: transparent;
}

// Profile page specific customizations (if any)
// Most styles are now handled by the global theme system

// Profile page specific customizations (if any)
// Role badge classes are handled by the TypeScript component

// All styles are now handled by the global theme system
// This file is kept minimal for any profile-specific overrides

// Edit Profile Modal Styles
// =============================================================================
.edit-profile-modal {
  .modal-wrapper {
    background: white !important;
  }
}

.modal-toolbar {
  --background: white;
  background: white;
}

.modal-content {
  --background: white;
  background: white;
}

.edit-form {
  background: white;

  .form-note {
    margin-top: var(--app-spacing-sm);
    font-size: var(--app-font-size-sm);
    color: var(--app-gray-500);
    font-style: italic;
    line-height: 1.4;
  }
}

.form-actions {
  display: flex;
  flex-direction: column;
  gap: var(--app-spacing-md);
  margin-top: var(--app-spacing-xl);

  .app-button {
    margin: 0;

    &:first-child {
      order: 1;
    }

    &:last-child {
      order: 2;
    }
  }

  @media (min-width: 480px) {
    flex-direction: row-reverse;

    .app-button {
      flex: 1;

      &:first-child {
        order: 2;
      }

      &:last-child {
        order: 1;
      }
    }
  }
}
