import {Component, OnInit, ViewChild} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {
  IonButton,
  IonContent,
  IonIcon,
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonInput,
  IonItem,
  IonLabel,
  IonSelect,
  IonSelectOption
} from '@ionic/angular/standalone';
import {AuthService, Me} from "../../services/auth/auth.service";
import {Router} from "@angular/router";
import {Location} from '@angular/common';
import {Store} from '@ngrx/store';
import {Apollo} from 'apollo-angular';
import {UserCreateRequestDto, UserStatus} from '../../models/user.model';
import {SAVE_USER} from '../../services/graphql/user.graphql';
import {NotificationService} from '../../services/notification.service';
import {addIcons} from 'ionicons';
import {
  arrowBackOutline,
  settingsOutline,
  cameraOutline,
  mailOutline,
  briefcaseOutline,
  businessOutline,
  calendarOutline,
  locationOutline,
  lockClosedOutline,
  pencilOutline,
  createOutline,
  logOutOutline,
  timeOutline,
  notificationsOutline,
  pulseOutline,
  shieldCheckmarkOutline,
  shieldOutline,
  shareOutline,
  chevronForwardOutline,
  personOutline,
  closeOutline,
  checkmarkOutline,
  saveOutline
} from 'ionicons/icons';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.page.html',
  styleUrls: ['./profile.page.scss'],
  standalone: true,
  imports: [
    IonContent,
    IonButton,
    IonIcon,
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonInput,
    IonItem,
    IonLabel,
    IonSelect,
    IonSelectOption,
    CommonModule,
    FormsModule,
    ReactiveFormsModule
  ]
})
export class ProfilePage implements OnInit {
  @ViewChild('editModal', { static: false }) editModal!: IonModal;

  // Current user data
  currentUser: Me | null = null;

  // Legacy user object for display (will be replaced with currentUser data)
  user = {
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'Technician',
    department: 'Engineering',
    memberSince: new Date('2023-01-15'),
    avatar: null as string | null
  };

  // Profile edit form
  profileForm: FormGroup;

  // Form state
  isLoading = false;
  isLoadingProfile = false;
  isSaving = false;

  // User status options
  userStatusOptions = [
    { value: UserStatus.ACTIVE, label: 'Active' },
    { value: UserStatus.INACTIVE, label: 'Inactive' },
    { value: UserStatus.PENDING, label: 'Pending' },
    { value: UserStatus.SUSPENDED, label: 'Suspended' }
  ];

  constructor(
    private authService: AuthService,
    private router: Router,
    private location: Location,
    private formBuilder: FormBuilder,
    private apollo: Apollo,
    private notificationService: NotificationService
  ) {
    // Initialize the profile form
    this.profileForm = this.createProfileForm();

    addIcons({
      'arrow-back-outline': arrowBackOutline,
      'settings-outline': settingsOutline,
      'camera-outline': cameraOutline,
      'mail-outline': mailOutline,
      'briefcase-outline': briefcaseOutline,
      'business-outline': businessOutline,
      'calendar-outline': calendarOutline,
      'location-outline': locationOutline,
      'lock-closed-outline': lockClosedOutline,
      'pencil-outline': pencilOutline,
      'create-outline': createOutline,
      'log-out-outline': logOutOutline,
      'time-outline': timeOutline,
      'notifications-outline': notificationsOutline,
      'pulse-outline': pulseOutline,
      'shield-checkmark-outline': shieldCheckmarkOutline,
      'shield-outline': shieldOutline,
      'share-outline': shareOutline,
      'chevron-forward-outline': chevronForwardOutline,
      'person': personOutline,
      'close-outline': closeOutline,
      'checkmark-outline': checkmarkOutline,
      'save-outline': saveOutline
    });
  }

  async ngOnInit() {
    console.log("Profile page initialized");
    await this.loadCurrentUser();
  }

  private createProfileForm(): FormGroup {
    return this.formBuilder.group({
      username: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(50)]],
      fullName: ['', [Validators.required, Validators.maxLength(100)]],
      email: ['', [Validators.required, Validators.email]],
      phoneCode: ['', [Validators.maxLength(5)]],
      phone: ['', [Validators.maxLength(15)]],
      password: [''], // Optional for updates
      status: [UserStatus.ACTIVE]
    });
  }

  private async loadCurrentUser() {
    this.isLoadingProfile = true;
    try {
      this.currentUser = await this.authService.me();
      console.log('Current user loaded:', this.currentUser);

      // Update legacy user object for display
      if (this.currentUser) {
        this.user.name = this.currentUser.fullName;
        this.user.email = this.currentUser.email;
        // Map roles to display string (take first role for now)
        this.user.role = this.currentUser.roles?.[0]?.displayName || 'User';
      }

      // Pre-populate the form with current user data
      this.populateForm();
    } catch (error) {
      console.error('Error loading current user:', error);
      this.notificationService.showError('Failed to load profile data');
    } finally {
      this.isLoadingProfile = false;
    }
  }

  private populateForm() {
    if (this.currentUser) {
      this.profileForm.patchValue({
        username: this.currentUser.username,
        fullName: this.currentUser.fullName,
        email: this.currentUser.email,
        phoneCode: this.currentUser.phoneCode || '',
        phone: this.currentUser.phone || '',
        status: this.currentUser.status || UserStatus.ACTIVE,
        password: '' // Always empty for security
      });
    }
  }

  goBack() {
    this.location.back();
  }

  openSettings() {
    this.router.navigate(['/menu/settings']);
  }

  editAvatar() {
    console.log('Edit avatar clicked');
    // TODO: Implement avatar editing functionality
  }

  editField(field: string) {
    console.log('Edit field clicked:', field);
    this.openEditModal();
  }

  openEditModal() {
    // Refresh form data before opening modal
    this.populateForm();
    this.editModal.present();
  }

  closeEditModal() {
    this.editModal.dismiss();
  }

  async saveProfile() {
    if (this.profileForm.invalid || !this.currentUser) {
      this.markFormGroupTouched(this.profileForm);
      return;
    }

    this.isSaving = true;

    try {
      const formValue = this.profileForm.value;

      // Create update DTO following UserCreateRequestDto structure
      const updateDto: UserCreateRequestDto = {
        uuid: this.currentUser.userId, // Use userId as uuid for update
        username: formValue.username,
        fullName: formValue.fullName,
        email: formValue.email,
        phoneCode: formValue.phoneCode || undefined,
        phone: formValue.phone || undefined,
        status: formValue.status,
        // Preserve existing roles - don't change them
        roleUuids: this.currentUser.roles.map(role => role.uuid)
      };

      // Only include password if it was provided
      if (formValue.password && formValue.password.trim()) {
        updateDto.password = formValue.password;
      }

      console.log('Updating profile with DTO:', updateDto);

      // Call GraphQL mutation
      const result = await this.apollo.mutate<any>({
        mutation: SAVE_USER,
        variables: { userDto: updateDto }
      }).toPromise();

      const response = result?.data?.saveUser;
      if (response?.code === 'SUCCESS') {
        this.notificationService.showSuccess('Profile updated successfully');

        // Reload current user data
        await this.loadCurrentUser();

        // Close modal
        this.closeEditModal();
      } else {
        throw new Error(response?.errorDescription || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      this.notificationService.showError('Failed to update profile');
    } finally {
      this.isSaving = false;
    }
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  viewActivity() {
    console.log('View activity clicked');
    // TODO: Navigate to activity page
  }

  viewNotifications() {
    console.log('View notifications clicked');
    // TODO: Navigate to notifications page
  }

  viewSecurity() {
    console.log('View security clicked');
    // TODO: Navigate to security settings page
  }

  getRoleBadgeClass(role: string): string {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'app-badge-danger';
      case 'manager':
        return 'app-badge-warning';
      case 'technician':
        return 'app-badge-success';
      default:
        return 'app-badge-gray';
    }
  }

  logout() {
    this.authService.logout();
  }

  // Helper method to get form control errors
  getFieldError(fieldName: string): string | null {
    const control = this.profileForm.get(fieldName);
    if (control && control.errors && control.touched) {
      if (control.errors['required']) return `${fieldName} is required`;
      if (control.errors['email']) return 'Please enter a valid email address';
      if (control.errors['minlength']) return `${fieldName} must be at least ${control.errors['minlength'].requiredLength} characters`;
      if (control.errors['maxlength']) return `${fieldName} must not exceed ${control.errors['maxlength'].requiredLength} characters`;
    }
    return null;
  }

  // Helper method to check if field has error
  hasFieldError(fieldName: string): boolean {
    const control = this.profileForm.get(fieldName);
    return !!(control && control.errors && control.touched);
  }

  // Helper method to get roles display string
  getRolesDisplayString(): string {
    if (!this.currentUser?.roles?.length) return '';
    return this.currentUser.roles.map(role => role.displayName).join(', ');
  }
}
