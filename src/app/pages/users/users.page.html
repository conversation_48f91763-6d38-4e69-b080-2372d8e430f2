<ion-content class="app-page has-gradient-bg list-page">
  <!-- Background -->
  <div class="app-background">
    <div class="app-floating-shapes"></div>
  </div>

  <!-- Main Content -->
  <div class="app-container">
    <div class="app-content app-content-wide">

      <!-- Header -->
      <div class="page-header">
        <div class="header-content">
          <h1 class="page-title">User Management</h1>
          <p class="page-subtitle">Manage system users, roles, and permissions</p>
        </div>

        <!-- Admin-only Add User Button -->
        <div class="header-actions">
          <ion-button
            *ngxPermissionsOnly="['ROLE_USERS_ADD', 'ROLE_USERS_VIEW']"
            class="app-button"
            (click)="openCreateModal()"
            id="add-user-trigger">
            <div class="app-button-content">
              <ion-icon name="add-outline" class="app-button-icon"></ion-icon>
              <span>Add User</span>
            </div>
          </ion-button>
        </div>
      </div>

      <!-- List Cards Container -->
      <div class="list-cards">
        <!-- Search and Filters Card -->
        <div class="app-card app-card-animated search-filters-card">
          <div class="search-filters">
            <!-- Search Bar -->
            <div class="search-container">
              <ion-searchbar
                [(ngModel)]="searchQuery"
                (ionInput)="onSearchInput($event)"
                placeholder="Search users by name or email..."
                show-clear-button="focus"
                class="app-searchbar">
              </ion-searchbar>
            </div>

            <!-- Filters -->
            <div class="filters-container">
              <div class="filter-chips">
                <ion-chip
                  [class.selected]="selectedRoleUuid === ''"
                  (click)="onRoleFilter('')"
                  class="filter-chip">
                  <ion-label>All Roles</ion-label>
                </ion-chip>

                <ion-chip
                  *ngFor="let role of availableRoles"
                  [class.selected]="selectedRoleUuid === role.uuid"
                  (click)="onRoleFilter(role.uuid)"
                  class="filter-chip">
                  <ion-label>{{ role.displayName }}</ion-label>
                </ion-chip>
              </div>

              <div class="filter-toggles">
                <ion-chip
                  [class.selected]="selectedStatus === userStatuses[0]"
                  (click)="onStatusFilter(userStatuses[0])"
                  class="filter-chip">
                  <ion-icon name="checkmark-circle-outline"></ion-icon>
                  <ion-label>Active</ion-label>
                </ion-chip>

                <ion-chip
                  [class.selected]="selectedStatus === userStatuses[1]"
                  (click)="onStatusFilter(userStatuses[1])"
                  class="filter-chip">
                  <ion-icon name="close-circle-outline"></ion-icon>
                  <ion-label>Inactive</ion-label>
                </ion-chip>

                <ion-chip
                  [class.selected]="selectedStatus === ''"
                  (click)="onStatusFilter('')"
                  class="filter-chip">
                  <ion-icon name="list-outline"></ion-icon>
                  <ion-label>All Status</ion-label>
                </ion-chip>
              </div>
            </div>
          </div>
        </div>

        <!-- Users List Card -->
        <div class="app-card app-card-animated list-content-card">
        <!-- Pull to Refresh -->
        <ion-refresher slot="fixed" (ionRefresh)="onRefresh($event)">
          <ion-refresher-content></ion-refresher-content>
        </ion-refresher>

        <!-- Loading State -->
        <div *ngIf="loading" class="loading-container">
          <div class="skeleton-list">
            <div *ngFor="let item of [1,2,3,4,5]" class="skeleton-item">
              <ion-skeleton-text animated style="width: 60px; height: 60px; border-radius: 50%;"></ion-skeleton-text>
              <div class="skeleton-content">
                <ion-skeleton-text animated style="width: 40%;"></ion-skeleton-text>
                <ion-skeleton-text animated style="width: 60%;"></ion-skeleton-text>
                <ion-skeleton-text animated style="width: 30%;"></ion-skeleton-text>
              </div>
            </div>
          </div>
        </div>

        <!-- Users List -->
        <div *ngIf="!loading" class="users-list">
          <div *ngIf="filteredUsers.length === 0" class="empty-state">
            <ion-icon name="person-outline" class="empty-icon"></ion-icon>
            <h3>No users found</h3>
            <p>Try adjusting your search or filters</p>
          </div>

          <ion-list *ngIf="filteredUsers.length > 0" class="list-items" role="feed">
            <ion-item
              *ngFor="let user of filteredUsers; trackBy: trackByUserUuid"
              class="list-item user-item"
              role="article"
              button>

              <!-- User Avatar -->
              <ion-avatar slot="start" class="user-avatar">
                <img *ngIf="user.profileImage" [src]="user.profileImage" [alt]="user.fullName">
                <div *ngIf="!user.profileImage" class="avatar-initials">
                  {{ getUserInitials(user.fullName) }}
                </div>
              </ion-avatar>

              <!-- User Info -->
              <ion-label class="user-info">
                <div class="user-name">
                  <h2>{{ user.fullName }}</h2>
                  <ion-badge
                    [color]="getStatusBadgeColor(user.status)"
                    class="status-badge">
                    {{ user.status }}
                  </ion-badge>
                </div>

                <div class="user-details">
                  <p class="user-email">
                    <ion-icon name="mail-outline"></ion-icon>
                    {{ user.email }}
                  </p>

                  <p class="user-phone" *ngIf="user.phone">
                    <ion-icon name="call-outline"></ion-icon>
                    <span *ngIf="user.phoneCode">{{ user.phoneCode }} </span>{{ user.phone }}
                  </p>
                </div>

                <div class="user-meta">
                  <div class="role-chips" *ngIf="user.roles.length > 0">
                    <ion-chip
                      *ngFor="let role of user.roles"
                      color="primary"
                      class="role-chip">
                      <ion-icon name="shield-outline"></ion-icon>
                      <ion-label>{{ role.displayName }}</ion-label>
                    </ion-chip>
                  </div>
                </div>
              </ion-label>

              <!-- Actions (Admin Only) -->
              <div slot="end" class="user-actions" *ngxPermissionsOnly="['ADMIN', 'USER_MANAGEMENT']">
                <ion-button
                  fill="clear"
                  size="small"
                  (click)="openEditModal(user); $event.stopPropagation()"
                  class="action-button">
                  <ion-icon name="create-outline" slot="icon-only"></ion-icon>
                </ion-button>

                <ion-button
                  *ngIf="canToggleUserStatus(user)"
                  fill="clear"
                  size="small"
                  [color]="user.status === 'ACTIVE' ? 'medium' : 'success'"
                  (click)="user.status === 'ACTIVE' ? deactivateUser(user) : activateUser(user); $event.stopPropagation()"
                  class="action-button">
                  <ion-icon
                    [name]="user.status === 'ACTIVE' ? 'close-circle-outline' : 'checkmark-circle-outline'"
                    slot="icon-only">
                  </ion-icon>
                </ion-button>

                <ion-button
                  fill="clear"
                  size="small"
                  color="danger"
                  (click)="deleteUser(user); $event.stopPropagation()"
                  class="action-button">
                  <ion-icon name="trash-outline" slot="icon-only"></ion-icon>
                </ion-button>
              </div>
            </ion-item>
          </ion-list>

          <!-- End of List Message -->
          <div *ngIf="filteredUsers.length > 0" class="end-of-list">
            <div class="end-message">
              <ion-icon name="checkmark-circle-outline" class="end-icon"></ion-icon>
              <p>{{ filteredUsers.length }} users loaded</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </div>

  <!-- User Modal (Create/Edit) -->
  <ion-modal [isOpen]="isModalOpen" (didDismiss)="closeModal()">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-buttons slot="start">
            <ion-button (click)="closeModal()">Cancel</ion-button>
          </ion-buttons>
          <ion-title>{{ modalMode === 'create' ? 'Add New User' : 'Edit User' }}</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="saveUser()" [strong]="true" [disabled]="userForm.invalid || loading">
              {{ loading ? 'Saving...' : (modalMode === 'create' ? 'Create' : 'Update') }}
            </ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>

      <ion-content class="ion-padding">
        <form [formGroup]="userForm" (ngSubmit)="saveUser()">

          <!-- Username Field -->
          <ion-item>
            <ion-input
              label="Username *"
              labelPlacement="stacked"
              type="text"
              formControlName="username"
              placeholder="Enter username"
              [class.ion-invalid]="userForm.get('username')?.invalid && userForm.get('username')?.touched"
              errorText="Username is required">
            </ion-input>
          </ion-item>

          <!-- Full Name Field -->
          <ion-item>
            <ion-input
              label="Full Name *"
              labelPlacement="stacked"
              type="text"
              formControlName="fullName"
              placeholder="Enter full name"
              [class.ion-invalid]="userForm.get('fullName')?.invalid && userForm.get('fullName')?.touched"
              errorText="Full name is required">
            </ion-input>
          </ion-item>

          <!-- Email Field -->
          <ion-item>
            <ion-input
              label="Email Address *"
              labelPlacement="stacked"
              type="email"
              formControlName="email"
              placeholder="Enter email address"
              [class.ion-invalid]="userForm.get('email')?.invalid && userForm.get('email')?.touched"
              errorText="Please enter a valid email">
            </ion-input>
          </ion-item>

          <!-- Phone Code Field -->
          <ion-item>
            <ion-input
              label="Phone Code"
              labelPlacement="stacked"
              type="text"
              formControlName="phoneCode"
              placeholder="e.g., +1"
              maxlength="5">
            </ion-input>
          </ion-item>

          <!-- Phone Number Field -->
          <ion-item>
            <ion-input
              label="Phone Number"
              labelPlacement="stacked"
              type="tel"
              formControlName="phone"
              placeholder="Enter phone number"
              maxlength="15">
            </ion-input>
          </ion-item>

          <!-- Roles Field -->
          <ion-item>
            <ion-select
              formControlName="roleUuids"
              aria-label="User roles"
              placeholder="Select all roles that apply"
              [multiple]="true">
              <ion-select-option *ngFor="let role of availableRoles" [value]="role.uuid">
                {{ role.displayName }}
              </ion-select-option>
            </ion-select>
          </ion-item>

          <!-- Password Field (Create Only) -->
          <ion-item *ngIf="modalMode === 'create'">
            <ion-input
              label="Password *"
              labelPlacement="stacked"
              type="password"
              formControlName="password"
              placeholder="Enter password"
              [class.ion-invalid]="userForm.get('password')?.invalid && userForm.get('password')?.touched"
              errorText="Password must be at least 6 characters">
            </ion-input>
          </ion-item>

          <!-- Status Field (Edit Only) -->
          <ion-item *ngIf="modalMode === 'edit'">
            <ion-select
              formControlName="status"
              placeholder="Select status">
              <ion-select-option *ngFor="let status of userStatuses" [value]="status">
                {{ status }}
              </ion-select-option>
            </ion-select>
          </ion-item>


        </form>
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
