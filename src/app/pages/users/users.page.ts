import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import {
  IonContent,
  IonHeader,
  IonTitle,
  IonToolbar,
  IonSearchbar,
  IonButton,
  IonIcon,
  IonList,
  IonItem,
  IonLabel,
  IonAvatar,
  IonBadge,
  IonChip,
  IonModal,
  IonButtons,
  IonInput,
  IonSelect,
  IonSelectOption,
  IonRefresher,
  IonRefresherContent,
  IonSkeletonText,
} from '@ionic/angular/standalone';
import { NgxPermissionsModule } from 'ngx-permissions';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';
import { addIcons } from 'ionicons';
import {
  addOutline,
  searchOutline,
  personOutline,
  mailOutline,
  callOutline,
  businessOutline,
  shieldOutline,
  createOutline,
  trashOutline,
  checkmarkCircleOutline,
  closeCircleOutline,
  ellipsisVerticalOutline,
  filterOutline,
  refreshOutline,
  closeOutline,
  saveOutline,
  lockClosedOutline,
  listOutline
} from 'ionicons/icons';

import {
  User,
  UserStatus,
  UserFilterDto,
  UserRole
} from '../../models/user.model';
import { AppState } from '../../store';
import * as UserActions from '../../store/users/user.actions';
import * as UserSelectors from '../../store/users/user.selectors';
import * as RoleActions from '../../store/roles/role.actions';
import * as RoleSelectors from '../../store/roles/role.selectors';
import { Role } from '../../models/role.model';

@Component({
  selector: 'app-users',
  templateUrl: './users.page.html',
  styleUrls: ['./users.page.scss'],
  standalone: true,
  imports: [
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonSearchbar,
    IonButton,
    IonIcon,
    IonList,
    IonItem,
    IonLabel,
    IonAvatar,
    IonBadge,
    IonChip,
    IonModal,
    IonButtons,
    IonInput,
    IonSelect,
    IonSelectOption,
    IonRefresher,
    IonRefresherContent,
    IonSkeletonText,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPermissionsModule
  ]
})
export class UsersPage implements OnInit, OnDestroy {
  users: User[] = [];
  filteredUsers: User[] = [];
  loading = false;
  searchQuery = '';
  selectedRoleUuid = '';
  selectedStatus: UserStatus | '' = '';

  // Modal state
  isModalOpen = false;
  modalMode: 'create' | 'edit' = 'create';
  selectedUser: User | null = null;
  userForm: FormGroup;

  // Constants
  userStatuses = Object.values(UserStatus);

  // NgRx Observables
  users$: Observable<User[]>;
  loading$: Observable<boolean>;
  error$: Observable<any>;
  roles$: Observable<Role[]>;
  availableRoles: Role[] = [];

  // Filters
  currentFilters: UserFilterDto = {
    page: 0,
    size: 10,
    sortBy: 'createdAt',
    sortDirection: 'DESC'
  };

  private destroy$ = new Subject<void>();
  private searchSubject = new Subject<string>();

  constructor(
    private formBuilder: FormBuilder,
    private store: Store<AppState>
  ) {
    // Add icons
    addIcons({
      addOutline,
      searchOutline,
      personOutline,
      mailOutline,
      callOutline,
      businessOutline,
      shieldOutline,
      createOutline,
      trashOutline,
      checkmarkCircleOutline,
      closeCircleOutline,
      ellipsisVerticalOutline,
      filterOutline,
      refreshOutline,
      closeOutline,
      saveOutline,
      lockClosedOutline,
      listOutline
    });

    // Initialize NgRx observables
    this.users$ = this.store.select(UserSelectors.selectAllUsers);
    this.loading$ = this.store.select(UserSelectors.selectUserLoading);
    this.error$ = this.store.select(UserSelectors.selectUserError);
    this.roles$ = this.store.select(RoleSelectors.selectAllRoles);

    // Initialize form
    this.userForm = this.createUserForm();

    // Setup search debouncing
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(query => {
      this.updateFilters({ search: query });
    });
  }

  ngOnInit() {
    // Load initial data with simplified filters
    const simpleFilters: UserFilterDto = {
      page: 0,
      size: 10,
      sortBy: 'createdAt',
      sortDirection: 'DESC'
    };

    console.log('Dispatching loadUsers with filters:', simpleFilters);
    this.store.dispatch(UserActions.loadUsers({ filters: simpleFilters }));

    // Load roles for the select dropdown
    console.log('Dispatching loadRoles');
    this.store.dispatch(RoleActions.loadRoles());

    // Subscribe to users and roles
    this.users$.pipe(takeUntil(this.destroy$)).subscribe(users => {
      console.log('Users received in component:', users);
      this.users = users;
      this.applyFilters();
    });

    this.roles$.pipe(takeUntil(this.destroy$)).subscribe(roles => {
      console.log('Roles received in component:', roles);
      this.availableRoles = roles;
    });

    this.loading$.pipe(takeUntil(this.destroy$)).subscribe(loading => {
      this.loading = loading;
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createUserForm(): FormGroup {
    return this.formBuilder.group({
      username: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(50)]],
      fullName: ['', [Validators.required, Validators.maxLength(100)]],
      email: ['', [Validators.required, Validators.email]],
      phoneCode: ['', [Validators.maxLength(5)]],
      phone: ['', [Validators.maxLength(15)]],
      roleUuids: [[]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      status: [UserStatus.ACTIVE]
    });
  }

  // Update filters and reload users
  updateFilters(newFilters: Partial<UserFilterDto>) {
    this.currentFilters = {
      ...this.currentFilters,
      ...newFilters,
      page: 0 // Reset to first page when filters change
    };
    this.store.dispatch(UserActions.loadUsers({ filters: this.currentFilters }));
  }

  // Handle search input
  onSearchInput(event: any) {
    const query = event.target.value;
    this.searchQuery = query;
    this.searchSubject.next(query);
  }

  // Apply local filters (for display purposes)
  applyFilters() {
    this.filteredUsers = this.users.filter(user => {
      const roleMatch = !this.selectedRoleUuid ||
        user.roles.some(role => role.uuid === this.selectedRoleUuid);
      const statusMatch = !this.selectedStatus || user.status === this.selectedStatus;
      return roleMatch && statusMatch;
    });
  }

  // Filter by role
  onRoleFilter(roleUuid: string) {
    this.selectedRoleUuid = roleUuid;
    this.updateFilters({ roleUuid: roleUuid || undefined });
  }

  // Filter by status
  onStatusFilter(status: UserStatus | '') {
    this.selectedStatus = status;
    this.updateFilters({ status: status || undefined });
  }

  // Refresh users
  onRefresh(event: any) {
    this.store.dispatch(UserActions.loadUsers({ filters: this.currentFilters }));
    setTimeout(() => {
      event.target.complete();
    }, 1000);
  }

  // Open create user modal
  openCreateModal() {
    this.modalMode = 'create';
    this.selectedUser = null;
    this.userForm.reset({
      status: UserStatus.ACTIVE,
      roleUuids: []
    });
    this.isModalOpen = true;
  }

  // Open edit user modal
  openEditModal(user: User) {
    this.modalMode = 'edit';
    this.selectedUser = user;
    this.userForm.patchValue({
      username: user.username,
      fullName: user.fullName,
      email: user.email,
      phoneCode: user.phoneCode || '',
      phone: user.phone,
      roleUuids: user.roles.map(role => role.uuid),
      status: user.status
    });
    // Remove password requirement for edit
    this.userForm.get('password')?.clearValidators();
    this.userForm.get('password')?.updateValueAndValidity();
    this.isModalOpen = true;
  }

  // Close modal
  closeModal() {
    this.isModalOpen = false;
    this.selectedUser = null;
    this.userForm.reset();
    this.store.dispatch(UserActions.clearUserError());
    // Restore password validators for next create
    this.userForm.get('password')?.setValidators([Validators.required, Validators.minLength(6)]);
    this.userForm.get('password')?.updateValueAndValidity();
  }

  // Save user (create or update)
  saveUser() {
    if (this.userForm.valid) {
      const formValue = this.userForm.value;

      if (this.modalMode === 'create') {
        // Create DTO matching UserCreateRequestDto
        const createDto = {
          username: formValue.username,
          password: formValue.password,
          fullName: formValue.fullName,
          email: formValue.email,
          phoneCode: formValue.phoneCode || undefined,
          phone: formValue.phone || undefined,
          status: formValue.status,
          roleUuids: formValue.roleUuids || []
        };

        this.store.dispatch(UserActions.saveUser({ userDto: createDto }));
      } else {
        // Update DTO matching UserCreateRequestDto (with uuid for update)
        const updateDto = {
          uuid: this.selectedUser!.uuid,
          username: formValue.username,
          fullName: formValue.fullName,
          email: formValue.email,
          phoneCode: formValue.phoneCode || undefined,
          phone: formValue.phone || undefined,
          status: formValue.status,
          roleUuids: formValue.roleUuids || []
          // No password for updates
        };

        this.store.dispatch(UserActions.saveUser({ userDto: updateDto }));
      }
      this.closeModal();
    }
  }

  // Deactivate user
  deactivateUser(user: User) {
    this.store.dispatch(UserActions.deactivateUser({ uuid: user.uuid }));
  }

  // Activate user
  activateUser(user: User) {
    this.store.dispatch(UserActions.activateUser({ uuid: user.uuid }));
  }

  // Delete user
  deleteUser(user: User) {
    this.store.dispatch(UserActions.deleteUser({ uuid: user.uuid }));
  }

  // Assign roles to user
  assignRoles(user: User, roleUuids: string[]) {
    const assignRolesDto = {
      userUuid: user.uuid,
      roleUuids
    };
    this.store.dispatch(UserActions.assignRolesToUser({ assignRolesDto }));
  }

  // Get status badge color
  getStatusBadgeColor(status: UserStatus): string {
    switch (status) {
      case UserStatus.ACTIVE: return 'success';
      case UserStatus.INACTIVE: return 'medium';
      case UserStatus.PENDING: return 'warning';
      case UserStatus.SUSPENDED: return 'danger';
      default: return 'medium';
    }
  }

  // Get user initials for avatar
  getUserInitials(fullName: string): string {
    return fullName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  }

  // Get role names as comma-separated string
  getRoleNames(roles: UserRole[]): string {
    return roles.map(role => role.displayName).join(', ');
  }

  // Check if user can be activated/deactivated
  canToggleUserStatus(user: User): boolean {
    return user.status === UserStatus.ACTIVE || user.status === UserStatus.INACTIVE;
  }

  // Track by function for ngFor performance
  trackByUserUuid(_index: number, user: User): string {
    return user.uuid;
  }
}
