<ion-split-pane contentId="menu-content" when="sm">
  <ion-menu contentId="menu-content">
    <ion-header class="ion-no-border">
      <ion-toolbar>
        <ion-title>Approval Flow</ion-title>
      </ion-toolbar>
    </ion-header>
    <ion-content>
      <ion-list>
        <ion-menu-toggle auto-hide="false" *ngFor="let p of appPages">
          <ion-item
            *ngxPermissionsOnly="p.permissions"
            [routerLink]="p.url"
            routerLinkActive="active-item"
            routerDirection="root"
            detail="false"
            lines="none"
          >
            <ion-icon slot="start" [name]="p.icon"></ion-icon>
            <ion-label>{{ p.title }}</ion-label>
          </ion-item>
        </ion-menu-toggle>
      </ion-list>
    </ion-content>
  </ion-menu>
  <ion-router-outlet id="menu-content"></ion-router-outlet>
</ion-split-pane>
