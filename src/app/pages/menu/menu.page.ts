import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {RouterLink, RouterLinkActive} from '@angular/router';
import {
  IonContent,
  IonHeader,
  IonIcon,
  IonItem,
  IonLabel,
  IonList,
  IonMenu,
  IonMenuToggle,
  IonRouterOutlet,
  IonSplitPane,
  IonTitle,
  IonToolbar
} from '@ionic/angular/standalone';
import { NgxPermissionsModule } from 'ngx-permissions';
import {addIcons} from 'ionicons';
import {
  businessOutline,
  cashOutline,
  checkmarkCircleOutline,
  documentTextOutline,
  homeOutline,
  menuOutline,
  peopleOutline,
  personOutline,
  settingsOutline
} from 'ionicons/icons';
import { PERMISSIONS, MENU_PERMISSIONS } from '../../constants/permissions';

@Component({
  selector: 'app-menu',
  templateUrl: './menu.page.html',
  styleUrls: ['./menu.page.scss'],
  standalone: true,
  imports: [
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonSplitPane,
    IonMenu,
    IonRouterOutlet,
    IonList,
    IonItem,
    IonIcon,
    IonLabel,
    IonMenuToggle,
    CommonModule,
    FormsModule,
    RouterLink,
    RouterLinkActive,
    NgxPermissionsModule
  ]
})
export class MenuPage implements OnInit {
  public appPages = [
    {
      title: 'Dashboard',
      url: '/menu/dashboard',
      icon: 'home',
      permissions: MENU_PERMISSIONS.DASHBOARD
    },
    {
      title: 'Requests',
      url: '/menu/requests',
      icon: 'document-text',
      permissions: MENU_PERMISSIONS.REQUESTS
    },
    // {
    //   title: 'Approvals',
    //   url: '/menu/approvals',
    //   icon: 'checkmark-circle',
    //   permissions: MENU_PERMISSIONS.APPROVALS
    // },
    {
      title: 'Users',
      url: '/menu/users',
      icon: 'people',
      permissions: MENU_PERMISSIONS.USERS
    },
    {
      title: 'Roles',
      url: '/menu/roles',
      icon: 'business',
      permissions: MENU_PERMISSIONS.ROLES
    },
    // {
    //   title: 'Expenses',
    //   url: '/menu/expense-form',
    //   icon: 'cash',
    //   permissions: MENU_PERMISSIONS.EXPENSES
    // },
    {
      title: 'Profile',
      url: '/menu/profile',
      icon: 'person',
      permissions: MENU_PERMISSIONS.PROFILE
    },
    // {
    //   title: 'Settings',
    //   url: '/menu/settings',
    //   icon: 'settings',
    //   permissions: MENU_PERMISSIONS.SETTINGS
    // },
  ];

  constructor() {
    addIcons({
      'home': homeOutline,
      'document-text': documentTextOutline,
      'checkmark-circle': checkmarkCircleOutline,
      'people': peopleOutline,
      'business': businessOutline,
      'person': personOutline,
      'settings': settingsOutline,
      'menu': menuOutline,
      'cash': cashOutline
    });
  }

  ngOnInit() {
  }

}
