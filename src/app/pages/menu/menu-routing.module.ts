import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';

const routes: Routes = [
  {
    path: '',
    redirectTo: '/menu/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadComponent: () => import('../dashboard/dashboard.page').then(m => m.DashboardPage)
  },
  {
    path: 'requests',
    loadComponent: () => import('../requests/requests.page').then(m => m.RequestsPage)
  },
  {
    path: 'approvals',
    loadComponent: () => import('../approvals/approvals.page').then(m => m.ApprovalsPage)
  },
  {
    path: 'users',
    loadComponent: () => import('../users/users.page').then(m => m.UsersPage)
  },
  {
    path: 'profile',
    loadComponent: () => import('../profile/profile.page').then(m => m.ProfilePage)
  },
  {
    path: 'settings',
    loadComponent: () => import('../settings/settings.page').then(m => m.SettingsPage)
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MenuPageRoutingModule {}
