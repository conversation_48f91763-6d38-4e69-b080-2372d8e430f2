import { Component, <PERSON><PERSON>nit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormArray, FormControl } from '@angular/forms';
import {
  IonContent, IonHeader, IonTitle, IonToolbar, IonButtons,
  IonItem, IonLabel, IonList,
  IonButton, IonIcon, IonSearchbar, IonModal,
  IonInput, IonRefresher, IonRefresherContent,
  IonBadge, IonSkeletonText, IonCheckbox, IonText
} from '@ionic/angular/standalone';
import { NgxPermissionsModule } from 'ngx-permissions';
import { addIcons } from 'ionicons';
import {
  add, search, refresh, close, save, create, pencil,
  checkmarkCircle, alertCircle, business, businessOutline,
  addOutline, saveOutline, closeOutline, createOutline,
  searchOutline, calendarOutline, checkmarkCircleOutline } from 'ionicons/icons';
import { Store } from '@ngrx/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { AppState } from '../../store';
import { Role, RoleDto, Permission } from '../../models/role.model';
import * as RoleActions from '../../store/roles/role.actions';
import * as RoleSelectors from '../../store/roles/role.selectors';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.page.html',
  styleUrls: ['./roles.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPermissionsModule,
    IonContent, IonHeader, IonTitle, IonToolbar, IonButtons,
    IonItem, IonLabel, IonList,
    IonButton, IonIcon, IonSearchbar, IonModal,
    IonInput, IonRefresher, IonRefresherContent,
    IonBadge, IonSkeletonText, IonCheckbox, IonText
  ]
})
export class RolesPage implements OnInit, OnDestroy {
  roles: Role[] = [];
  filteredRoles: Role[] = [];
  searchTerm = '';
  loading = false;

  // Modal state
  isModalOpen = false;
  modalMode: 'create' | 'edit' = 'create';
  selectedRole: Role | null = null;
  roleForm: FormGroup;

  // NgRx Observables
  roles$: Observable<Role[]>;
  loading$: Observable<boolean>;
  error$: Observable<any>;
  permissions$: Observable<Permission[]>;
  permissionsLoading$: Observable<boolean>;
  permissionsError$: Observable<any>;
  permissionsByGroup$: Observable<{ [key: string]: Permission[] }>;

  // Permission state
  permissions: Permission[] = [];
  permissionsByGroup: { [key: string]: Permission[] } = {};

  private destroy$ = new Subject<void>();

  constructor(
    private store: Store<AppState>,
    private formBuilder: FormBuilder
  ) {
    // Add icons
    addIcons({addOutline,businessOutline,calendarOutline,createOutline,checkmarkCircleOutline,add,search,refresh,close,save,create,pencil,checkmarkCircle,alertCircle,business,saveOutline,closeOutline,searchOutline});

    // Initialize NgRx observables
    this.roles$ = this.store.select(RoleSelectors.selectAllRoles);
    this.loading$ = this.store.select(RoleSelectors.selectRoleLoading);
    this.error$ = this.store.select(RoleSelectors.selectRoleError);
    this.permissions$ = this.store.select(RoleSelectors.selectAllPermissions);
    this.permissionsLoading$ = this.store.select(RoleSelectors.selectPermissionsLoading);
    this.permissionsError$ = this.store.select(RoleSelectors.selectPermissionsError);
    this.permissionsByGroup$ = this.store.select(RoleSelectors.selectPermissionsByGroup);

    // Initialize form
    this.roleForm = this.createRoleForm();
  }

  private createRoleForm(): FormGroup {
    return this.formBuilder.group({
      displayName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      permissions: this.formBuilder.array([])
    });
  }

  ngOnInit() {
    // Load roles and permissions from the store
    this.store.dispatch(RoleActions.loadRoles());
    this.store.dispatch(RoleActions.loadPermissions());

    // Subscribe to roles and update filtered roles
    this.roles$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(roles => {
      this.roles = roles;
      this.applyFilters();
    });

    // Subscribe to loading state
    this.loading$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(loading => {
      this.loading = loading;
    });

    // Subscribe to permissions
    this.permissions$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(permissions => {
      this.permissions = permissions;
    });

    // Subscribe to permissions grouped by category
    this.permissionsByGroup$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(permissionsByGroup => {
      this.permissionsByGroup = permissionsByGroup;
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Method to apply search filter
  applyFilters() {
    this.filteredRoles = this.roles.filter(role => {
      if (this.searchTerm && this.searchTerm.trim() !== '') {
        const term = this.searchTerm.toLowerCase();
        return (
          role.displayName.toLowerCase().includes(term) ||
          role.name.toLowerCase().includes(term)
        );
      }
      return true;
    });
  }

  // Method to handle search input
  onSearchChange(event: any) {
    this.searchTerm = event.detail.value;
    this.applyFilters();
  }

  // Clear search
  clearSearch() {
    this.searchTerm = '';
    this.applyFilters();
  }

  // Refresh roles
  onRefresh(event: any) {
    this.store.dispatch(RoleActions.loadRoles());
    setTimeout(() => {
      event.target.complete();
    }, 1000);
  }

  // Get permissions form array
  get permissionsFormArray(): FormArray {
    return this.roleForm.get('permissions') as FormArray;
  }

  // Initialize permissions checkboxes
  private initializePermissions(selectedPermissionUuids: string[] = []) {
    const permissionsArray = this.permissionsFormArray;
    permissionsArray.clear();

    this.permissions.forEach(permission => {
      const isSelected = selectedPermissionUuids.includes(permission.uuid);
      permissionsArray.push(new FormControl(isSelected));
    });
  }

  // Get selected permission UUIDs
  private getSelectedPermissionUuids(): string[] {
    const selectedUuids: string[] = [];
    const permissionsArray = this.permissionsFormArray;

    permissionsArray.controls.forEach((control, index) => {
      if (control.value && this.permissions[index]) {
        selectedUuids.push(this.permissions[index].uuid);
      }
    });

    return selectedUuids;
  }

  // Open create role modal
  openCreateModal() {
    this.modalMode = 'create';
    this.selectedRole = null;
    this.roleForm.reset();
    this.initializePermissions(); // Initialize with no permissions selected
    this.isModalOpen = true;
  }

  // Open edit role modal
  openEditModal(role: Role) {
    this.modalMode = 'edit';
    this.selectedRole = role;
    this.roleForm.patchValue({
      displayName: role.displayName
    });
    // Initialize permissions with role's current permissions
    const rolePermissionUuids = role.permissions?.map(p => p.uuid) || [];
    this.initializePermissions(rolePermissionUuids);
    this.isModalOpen = true;
  }

  // Close modal
  closeModal() {
    this.isModalOpen = false;
    this.selectedRole = null;
    this.roleForm.reset();
    this.store.dispatch(RoleActions.clearRoleError());
  }

  // Save role (create or update)
  saveRole() {
    if (this.roleForm.valid) {
      const formValue = this.roleForm.value;
      const selectedPermissionUuids = this.getSelectedPermissionUuids();

      if (this.modalMode === 'create') {
        const roleDto: RoleDto = {
          displayName: formValue.displayName.trim(),
          permissionUuids: selectedPermissionUuids
        };
        this.store.dispatch(RoleActions.createRole({ roleDto }));
      } else if (this.selectedRole) {
        const roleDto: RoleDto = {
          uuid: this.selectedRole.uuid,
          displayName: formValue.displayName.trim(),
          permissionUuids: selectedPermissionUuids
        };
        this.store.dispatch(RoleActions.updateRole({ roleDto }));
      }

      this.closeModal();
    }
  }

  // Track by function for ngFor
  trackByRoleId(_index: number, role: Role): string {
    return role.uuid;
  }

  // Track by function for permission groups
  trackByGroupName(_index: number, item: any): string {
    return item.key;
  }

  // Track by function for permissions
  trackByPermissionId(_index: number, permission: Permission): string {
    return permission.uuid;
  }

  // Get permission index in the form array
  getPermissionIndex(permissionUuid: string): number {
    return this.permissions.findIndex(p => p.uuid === permissionUuid);
  }
}
