// Request Details Page Specific Styles
// Using global styling system - minimal custom styles needed

.request-meta-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  @media (min-width: 768px) {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }
}

.approval-comment {
  padding: 1rem;
  background: var(--app-gray-50);
  border-radius: var(--app-radius-lg);
  border: 1px solid var(--app-gray-200);

  h4 {
    margin: 0;
  }

  p {
    margin: 0;
    line-height: 1.5;
  }
}

.action-buttons-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;

  @media (min-width: 768px) {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
}