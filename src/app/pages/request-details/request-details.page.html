<ion-content [fullscreen]="true" class="app-page has-gradient-bg">
  <!-- Background Elements -->
  <div class="app-background">
    <div class="app-floating-shapes">
      <div class="shape shape-1"></div>
      <div class="shape shape-2"></div>
      <div class="shape shape-3"></div>
    </div>
  </div>

  <!-- Main Request Details Container -->
  <div class="app-container">
    <div class="app-content app-content-wide">
      <!-- Header Section -->
      <div class="app-header">
        <div class="header-left">
          <ion-button fill="clear" class="app-header-button" (click)="goBack()">
            <ion-icon name="arrow-back-outline" slot="icon-only"></ion-icon>
          </ion-button>
          <h1 class="header-title">Request Details</h1>
        </div>
        <div class="header-right">
          <ion-button fill="clear" class="app-header-button">
            <ion-icon name="ellipsis-horizontal-outline" slot="icon-only"></ion-icon>
          </ion-button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading$ | async" class="app-card text-center">
        <div class="loading-content">
          <ion-spinner name="crescent" class="loading-spinner"></ion-spinner>
          <h3 class="text-lg font-semibold text-gray-800 mt-md mb-xs">Loading Request Details</h3>
          <p class="text-gray-600 text-sm m-0">Please wait while we fetch the request information...</p>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="error$ | async as error" class="app-card text-center">
        <div class="error-content">
          <div class="error-icon mb-md">
            <ion-icon name="alert-circle-outline" class="text-danger"></ion-icon>
          </div>
          <h3 class="text-lg font-semibold text-gray-800 mb-xs">Error Loading Request</h3>
          <p class="text-gray-600 text-sm mb-lg">{{ error?.message || 'Please try again later' }}</p>
          <ion-button class="app-button app-button-outline" (click)="loadRequest()">
            <div class="app-button-content">
              <ion-icon name="refresh-outline" class="app-button-icon"></ion-icon>
              <span>Try Again</span>
            </div>
          </ion-button>
        </div>
      </div>

      <!-- Request Details Content -->
      <div *ngIf="!(loading$ | async) && !(error$ | async) && request">
        <!-- Request Overview Card -->
        <div class="app-card mb-lg">
          <div class="card-header mb-lg">
            <h2 class="text-2xl font-bold text-gray-800 m-0">{{ request.title }}</h2>
            <div class="flex items-center gap-sm mt-xs">
              <div class="app-badge" [ngClass]="getStatusBadgeClass(request.status)">
                {{ request.status }}
              </div>
              <p class="text-gray-600 text-sm m-0">{{ formatDate(request.requestedDate) }}</p>
            </div>
          </div>

          <div class="request-meta-grid">
            <div class="app-list-item compact">
              <div class="app-list-icon small">
                <ion-icon name="document-text-outline"></ion-icon>
              </div>
              <div class="app-list-content">
                <div class="app-list-title">Request ID</div>
                <div class="app-list-subtitle">{{ request.uuid }}</div>
              </div>
            </div>

            <div *ngIf="request.approvedDate" class="app-list-item compact">
              <div class="app-list-icon small">
                <ion-icon name="checkmark-circle-outline"></ion-icon>
              </div>
              <div class="app-list-content">
                <div class="app-list-title">Approved Date</div>
                <div class="app-list-subtitle">{{ formatDate(request.approvedDate) }}</div>
              </div>
            </div>

            <div *ngIf="request.updatedAt" class="app-list-item compact">
              <div class="app-list-icon small">
                <ion-icon name="time-outline"></ion-icon>
              </div>
              <div class="app-list-content">
                <div class="app-list-title">Last Updated</div>
                <div class="app-list-subtitle">{{ formatDate(request.updatedAt) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Description Card -->
        <div class="app-card mb-lg">
          <div class="card-header mb-md">
            <h3 class="text-lg font-semibold text-gray-800 m-0">Description</h3>
          </div>
          <p class="text-gray-700 leading-relaxed m-0">{{ request.description }}</p>
        </div>

        <!-- Requester Information Card -->
        <div *ngIf="request.requestedBy" class="app-card mb-lg">
          <div class="card-header mb-md">
            <h3 class="text-lg font-semibold text-gray-800 m-0">Requested By</h3>
          </div>
          <div class="app-list-item">
            <div class="app-list-icon">
              <ion-icon name="person-outline"></ion-icon>
            </div>
            <div class="app-list-content">
              <div class="app-list-title">{{ request.requestedBy.fullName }}</div>
              <div class="app-list-subtitle">{{ request.requestedBy.email }}</div>
              <div class="app-list-subtitle">Username: {{ request.requestedBy.username }}</div>
            </div>
          </div>
        </div>

        <!-- Approval Information Card -->
        <div *ngIf="request.approvedBy || request.approvalComment" class="app-card mb-lg">
          <div class="card-header mb-md">
            <h3 class="text-lg font-semibold text-gray-800 m-0">Approval Information</h3>
          </div>

          <div *ngIf="request.approvedBy" class="app-list-item mb-md">
            <div class="app-list-icon">
              <ion-icon name="checkmark-circle-outline"></ion-icon>
            </div>
            <div class="app-list-content">
              <div class="app-list-title">Approved By</div>
              <div class="app-list-subtitle">{{ request.approvedBy.fullName }}</div>
              <div class="app-list-subtitle">{{ request.approvedBy.email }}</div>
            </div>
          </div>

          <div *ngIf="request.approvalComment" class="approval-comment">
            <h4 class="text-sm font-medium text-gray-700 mb-xs">Approval Comment:</h4>
            <p class="text-gray-600 text-sm m-0">{{ request.approvalComment }}</p>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="app-card">
          <div class="card-header mb-lg">
            <h3 class="text-lg font-semibold text-gray-800 m-0">Actions</h3>
            <p class="text-gray-600 text-sm m-0 mt-xs">Manage this request</p>
          </div>

          <div class="action-buttons-grid">
            <ion-button
              *ngIf="request.status === 'PENDING'"
              expand="block"
              class="app-button app-button-success"
              (click)="approveRequest()">
              <div class="app-button-content">
                <ion-icon name="checkmark-outline" class="app-button-icon"></ion-icon>
                <span>Approve Request</span>
              </div>
            </ion-button>

            <ion-button
              *ngIf="request.status === 'PENDING'"
              expand="block"
              class="app-button app-button-danger"
              (click)="rejectRequest()">
              <div class="app-button-content">
                <ion-icon name="close-outline" class="app-button-icon"></ion-icon>
                <span>Reject Request</span>
              </div>
            </ion-button>

            <ion-button
              expand="block"
              class="app-button app-button-outline"
              (click)="editRequest()">
              <div class="app-button-content">
                <ion-icon name="create-outline" class="app-button-icon"></ion-icon>
                <span>Edit Request</span>
              </div>
            </ion-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</ion-content>
