// Utility Classes
// =============================================================================
// Variables are already available through the global theme system

// Typography Utilities
// -----------------------------------------------------------------------------
.text-xs { font-size: var(--app-font-size-xs) !important; }
.text-sm { font-size: var(--app-font-size-sm) !important; }
.text-base { font-size: var(--app-font-size-base) !important; }
.text-lg { font-size: var(--app-font-size-lg) !important; }
.text-xl { font-size: var(--app-font-size-xl) !important; }
.text-2xl { font-size: var(--app-font-size-2xl) !important; }
.text-3xl { font-size: var(--app-font-size-3xl) !important; }
.text-4xl { font-size: var(--app-font-size-4xl) !important; }

.font-normal { font-weight: var(--app-font-weight-normal) !important; }
.font-medium { font-weight: var(--app-font-weight-medium) !important; }
.font-semibold { font-weight: var(--app-font-weight-semibold) !important; }
.font-bold { font-weight: var(--app-font-weight-bold) !important; }

.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

// Color Utilities
// -----------------------------------------------------------------------------
.text-primary { color: var(--app-primary) !important; }
.text-secondary { color: var(--app-secondary) !important; }
.text-white { color: var(--app-white) !important; }
.text-black { color: var(--app-black) !important; }
.text-gray-50 { color: var(--app-gray-50) !important; }
.text-gray-100 { color: var(--app-gray-100) !important; }
.text-gray-200 { color: var(--app-gray-200) !important; }
.text-gray-300 { color: var(--app-gray-300) !important; }
.text-gray-400 { color: var(--app-gray-400) !important; }
.text-gray-500 { color: var(--app-gray-500) !important; }
.text-gray-600 { color: var(--app-gray-600) !important; }
.text-gray-700 { color: var(--app-gray-700) !important; }
.text-gray-800 { color: var(--app-gray-800) !important; }
.text-gray-900 { color: var(--app-gray-900) !important; }
.text-success { color: var(--app-success) !important; }
.text-warning { color: var(--app-warning) !important; }
.text-danger { color: var(--app-danger) !important; }
.text-info { color: var(--app-info) !important; }

.bg-primary { background-color: var(--app-primary) !important; }
.bg-secondary { background-color: var(--app-secondary) !important; }
.bg-white { background-color: var(--app-white) !important; }
.bg-gray-50 { background-color: var(--app-gray-50) !important; }
.bg-gray-100 { background-color: var(--app-gray-100) !important; }
.bg-success { background-color: var(--app-success) !important; }
.bg-warning { background-color: var(--app-warning) !important; }
.bg-danger { background-color: var(--app-danger) !important; }
.bg-info { background-color: var(--app-info) !important; }

.bg-gradient-primary { background: var(--app-gradient-primary) !important; }
.bg-gradient-secondary { background: var(--app-gradient-secondary) !important; }

// Spacing Utilities
// -----------------------------------------------------------------------------
.m-0 { margin: 0 !important; }
.m-xs { margin: var(--app-spacing-xs) !important; }
.m-sm { margin: var(--app-spacing-sm) !important; }
.m-md { margin: var(--app-spacing-md) !important; }
.m-lg { margin: var(--app-spacing-lg) !important; }
.m-xl { margin: var(--app-spacing-xl) !important; }
.m-2xl { margin: var(--app-spacing-2xl) !important; }
.m-3xl { margin: var(--app-spacing-3xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-xs { margin-top: var(--app-spacing-xs) !important; }
.mt-sm { margin-top: var(--app-spacing-sm) !important; }
.mt-md { margin-top: var(--app-spacing-md) !important; }
.mt-lg { margin-top: var(--app-spacing-lg) !important; }
.mt-xl { margin-top: var(--app-spacing-xl) !important; }
.mt-2xl { margin-top: var(--app-spacing-2xl) !important; }
.mt-3xl { margin-top: var(--app-spacing-3xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-xs { margin-bottom: var(--app-spacing-xs) !important; }
.mb-sm { margin-bottom: var(--app-spacing-sm) !important; }
.mb-md { margin-bottom: var(--app-spacing-md) !important; }
.mb-lg { margin-bottom: var(--app-spacing-lg) !important; }
.mb-xl { margin-bottom: var(--app-spacing-xl) !important; }
.mb-2xl { margin-bottom: var(--app-spacing-2xl) !important; }
.mb-3xl { margin-bottom: var(--app-spacing-3xl) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-xs { margin-left: var(--app-spacing-xs) !important; }
.ml-sm { margin-left: var(--app-spacing-sm) !important; }
.ml-md { margin-left: var(--app-spacing-md) !important; }
.ml-lg { margin-left: var(--app-spacing-lg) !important; }
.ml-xl { margin-left: var(--app-spacing-xl) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-xs { margin-right: var(--app-spacing-xs) !important; }
.mr-sm { margin-right: var(--app-spacing-sm) !important; }
.mr-md { margin-right: var(--app-spacing-md) !important; }
.mr-lg { margin-right: var(--app-spacing-lg) !important; }
.mr-xl { margin-right: var(--app-spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-xs { padding: var(--app-spacing-xs) !important; }
.p-sm { padding: var(--app-spacing-sm) !important; }
.p-md { padding: var(--app-spacing-md) !important; }
.p-lg { padding: var(--app-spacing-lg) !important; }
.p-xl { padding: var(--app-spacing-xl) !important; }
.p-2xl { padding: var(--app-spacing-2xl) !important; }
.p-3xl { padding: var(--app-spacing-3xl) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-xs { padding-top: var(--app-spacing-xs) !important; }
.pt-sm { padding-top: var(--app-spacing-sm) !important; }
.pt-md { padding-top: var(--app-spacing-md) !important; }
.pt-lg { padding-top: var(--app-spacing-lg) !important; }
.pt-xl { padding-top: var(--app-spacing-xl) !important; }
.pt-2xl { padding-top: var(--app-spacing-2xl) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-xs { padding-bottom: var(--app-spacing-xs) !important; }
.pb-sm { padding-bottom: var(--app-spacing-sm) !important; }
.pb-md { padding-bottom: var(--app-spacing-md) !important; }
.pb-lg { padding-bottom: var(--app-spacing-lg) !important; }
.pb-xl { padding-bottom: var(--app-spacing-xl) !important; }
.pb-2xl { padding-bottom: var(--app-spacing-2xl) !important; }

.pl-0 { padding-left: 0 !important; }
.pl-xs { padding-left: var(--app-spacing-xs) !important; }
.pl-sm { padding-left: var(--app-spacing-sm) !important; }
.pl-md { padding-left: var(--app-spacing-md) !important; }
.pl-lg { padding-left: var(--app-spacing-lg) !important; }
.pl-xl { padding-left: var(--app-spacing-xl) !important; }

.pr-0 { padding-right: 0 !important; }
.pr-xs { padding-right: var(--app-spacing-xs) !important; }
.pr-sm { padding-right: var(--app-spacing-sm) !important; }
.pr-md { padding-right: var(--app-spacing-md) !important; }
.pr-lg { padding-right: var(--app-spacing-lg) !important; }
.pr-xl { padding-right: var(--app-spacing-xl) !important; }

// Layout Utilities
// -----------------------------------------------------------------------------
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.block { display: block !important; }
.inline-block { display: inline-block !important; }
.hidden { display: none !important; }

.flex-row { flex-direction: row !important; }
.flex-col { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.items-start { align-items: flex-start !important; }
.items-center { align-items: center !important; }
.items-end { align-items: flex-end !important; }
.items-stretch { align-items: stretch !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

.flex-1 { flex: 1 1 0% !important; }
.flex-auto { flex: 1 1 auto !important; }
.flex-none { flex: none !important; }

.gap-0 { gap: 0 !important; }
.gap-xs { gap: var(--app-spacing-xs) !important; }
.gap-sm { gap: var(--app-spacing-sm) !important; }
.gap-md { gap: var(--app-spacing-md) !important; }
.gap-lg { gap: var(--app-spacing-lg) !important; }
.gap-xl { gap: var(--app-spacing-xl) !important; }

// Position Utilities
// -----------------------------------------------------------------------------
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }

// Border Radius Utilities
// -----------------------------------------------------------------------------
.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: var(--app-radius-sm) !important; }
.rounded-md { border-radius: var(--app-radius-md) !important; }
.rounded-lg { border-radius: var(--app-radius-lg) !important; }
.rounded-xl { border-radius: var(--app-radius-xl) !important; }
.rounded-2xl { border-radius: var(--app-radius-2xl) !important; }
.rounded-full { border-radius: var(--app-radius-full) !important; }

// Shadow Utilities
// -----------------------------------------------------------------------------
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--app-shadow-sm) !important; }
.shadow-md { box-shadow: var(--app-shadow-md) !important; }
.shadow-lg { box-shadow: var(--app-shadow-lg) !important; }
.shadow-xl { box-shadow: var(--app-shadow-xl) !important; }
.shadow-2xl { box-shadow: var(--app-shadow-2xl) !important; }
.shadow-glass { box-shadow: var(--app-shadow-glass) !important; }

// Width & Height Utilities
// -----------------------------------------------------------------------------
.w-full { width: 100% !important; }
.w-auto { width: auto !important; }
.h-full { height: 100% !important; }
.h-auto { height: auto !important; }
.min-h-screen { min-height: 100vh !important; }

// Opacity Utilities
// -----------------------------------------------------------------------------
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

// Transform Utilities
// -----------------------------------------------------------------------------
.transform { transform: translateZ(0) !important; }
.scale-95 { transform: scale(0.95) !important; }
.scale-100 { transform: scale(1) !important; }
.scale-105 { transform: scale(1.05) !important; }
.scale-110 { transform: scale(1.1) !important; }

// Transition Utilities
// -----------------------------------------------------------------------------
.transition-none { transition: none !important; }
.transition-all { transition: all var(--app-transition-normal) !important; }
.transition-fast { transition: all var(--app-transition-fast) !important; }
.transition-slow { transition: all var(--app-transition-slow) !important; }

// Cursor Utilities
// -----------------------------------------------------------------------------
.cursor-pointer { cursor: pointer !important; }
.cursor-default { cursor: default !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

// Overflow Utilities
// -----------------------------------------------------------------------------
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-auto { overflow: auto !important; }

// Responsive Utilities
// -----------------------------------------------------------------------------
@media (max-width: 480px) {
  .mobile\:hidden { display: none !important; }
  .mobile\:block { display: block !important; }
  .mobile\:flex { display: flex !important; }
  .mobile\:text-center { text-align: center !important; }
  .mobile\:p-sm { padding: var(--app-spacing-sm) !important; }
  .mobile\:m-sm { margin: var(--app-spacing-sm) !important; }
}

@media (min-width: 768px) {
  .tablet\:hidden { display: none !important; }
  .tablet\:block { display: block !important; }
  .tablet\:flex { display: flex !important; }
  .tablet\:text-left { text-align: left !important; }
  .tablet\:p-lg { padding: var(--app-spacing-lg) !important; }
  .tablet\:m-lg { margin: var(--app-spacing-lg) !important; }
}

@media (min-width: 1024px) {
  .desktop\:hidden { display: none !important; }
  .desktop\:block { display: block !important; }
  .desktop\:flex { display: flex !important; }
}
