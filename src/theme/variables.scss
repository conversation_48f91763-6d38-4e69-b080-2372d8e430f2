// Modern App Theme Variables
// =============================================================================

// Color Palette
// -----------------------------------------------------------------------------
:root {
  // Primary Colors
  --app-primary: #4f46e5;
  --app-primary-rgb: 79, 70, 229;
  --app-primary-contrast: #ffffff;
  --app-primary-shade: #4338ca;
  --app-primary-tint: #6366f1;

  // Secondary Colors
  --app-secondary: #7c3aed;
  --app-secondary-rgb: 124, 58, 237;
  --app-secondary-contrast: #ffffff;
  --app-secondary-shade: #6d28d9;
  --app-secondary-tint: #8b5cf6;

  // Gradient Colors
  --app-gradient-start: #667eea;
  --app-gradient-end: #764ba2;
  --app-gradient-primary: linear-gradient(135deg, var(--app-gradient-start) 0%, var(--app-gradient-end) 100%);
  --app-gradient-secondary: linear-gradient(135deg, var(--app-primary) 0%, var(--app-secondary) 100%);

  // Neutral Colors
  --app-white: #ffffff;
  --app-black: #000000;
  --app-gray-50: #f9fafb;
  --app-gray-100: #f3f4f6;
  --app-gray-200: #e5e7eb;
  --app-gray-300: #d1d5db;
  --app-gray-400: #9ca3af;
  --app-gray-500: #6b7280;
  --app-gray-600: #4b5563;
  --app-gray-700: #374151;
  --app-gray-800: #1f2937;
  --app-gray-900: #111827;

  // Status Colors
  --app-success: #10b981;
  --app-success-light: rgba(16, 185, 129, 0.1);
  --app-warning: #f59e0b;
  --app-warning-light: rgba(245, 158, 11, 0.1);
  --app-danger: #ef4444;
  --app-danger-light: rgba(239, 68, 68, 0.1);
  --app-info: #3b82f6;
  --app-info-light: rgba(59, 130, 246, 0.1);

  // Typography
  --app-font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --app-font-size-xs: 0.75rem;
  --app-font-size-sm: 0.875rem;
  --app-font-size-base: 1rem;
  --app-font-size-lg: 1.125rem;
  --app-font-size-xl: 1.25rem;
  --app-font-size-2xl: 1.5rem;
  --app-font-size-3xl: 1.875rem;
  --app-font-size-4xl: 2.25rem;

  --app-font-weight-normal: 400;
  --app-font-weight-medium: 500;
  --app-font-weight-semibold: 600;
  --app-font-weight-bold: 700;

  // Spacing
  --app-spacing-xs: 0.25rem;
  --app-spacing-sm: 0.5rem;
  --app-spacing-md: 1rem;
  --app-spacing-lg: 1.5rem;
  --app-spacing-xl: 2rem;
  --app-spacing-2xl: 2.5rem;
  --app-spacing-3xl: 3rem;

  // Border Radius
  --app-radius-sm: 0.375rem;
  --app-radius-md: 0.5rem;
  --app-radius-lg: 0.75rem;
  --app-radius-xl: 1rem;
  --app-radius-2xl: 1.5rem;
  --app-radius-full: 9999px;

  // Shadows
  --app-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --app-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --app-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --app-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --app-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --app-shadow-glass: 0 32px 64px -12px rgba(0, 0, 0, 0.3);

  // Glass Morphism
  --app-glass-bg: rgba(255, 255, 255, 0.95);
  --app-glass-border: rgba(255, 255, 255, 0.2);
  --app-glass-backdrop: blur(20px);

  // Transitions
  --app-transition-fast: 0.15s ease;
  --app-transition-normal: 0.3s ease;
  --app-transition-slow: 0.5s ease;

  // Z-Index
  --app-z-background: 0;
  --app-z-content: 1;
  --app-z-overlay: 10;
  --app-z-modal: 100;
  --app-z-toast: 1000;
}
