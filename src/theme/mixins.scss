// Modern App Mixins
// =============================================================================

// Background Mixins
// -----------------------------------------------------------------------------
@mixin gradient-background() {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--app-z-background);
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--app-gradient-primary);
    opacity: 0.9;
  }
}

@mixin floating-shapes() {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
  
  .shape {
    position: absolute;
    border-radius: var(--app-radius-full);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: var(--app-glass-backdrop);
    animation: float 6s ease-in-out infinite;
    
    &.shape-1 {
      width: 120px;
      height: 120px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }
    
    &.shape-2 {
      width: 80px;
      height: 80px;
      top: 70%;
      right: 15%;
      animation-delay: 2s;
    }
    
    &.shape-3 {
      width: 60px;
      height: 60px;
      top: 30%;
      right: 25%;
      animation-delay: 4s;
    }
  }
}

// Glass Morphism Mixins
// -----------------------------------------------------------------------------
@mixin glass-card($padding: var(--app-spacing-xl)) {
  background: var(--app-glass-bg);
  backdrop-filter: var(--app-glass-backdrop);
  border-radius: var(--app-radius-2xl);
  padding: $padding;
  box-shadow: var(--app-shadow-2xl);
  border: 1px solid var(--app-glass-border);
  transition: all var(--app-transition-normal);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--app-shadow-glass);
  }
}

@mixin glass-button($size: medium) {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: var(--app-glass-backdrop);
  border: 1px solid var(--app-glass-border);
  color: var(--app-white);
  transition: all var(--app-transition-normal);
  
  @if $size == small {
    --border-radius: var(--app-radius-lg);
    width: 36px;
    height: 36px;
  } @else if $size == medium {
    --border-radius: var(--app-radius-xl);
    width: 48px;
    height: 48px;
  } @else if $size == large {
    --border-radius: var(--app-radius-xl);
    height: 56px;
    padding: 0 var(--app-spacing-lg);
  }
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }
}

// Layout Mixins
// -----------------------------------------------------------------------------
@mixin page-container() {
  position: relative;
  z-index: var(--app-z-content);
  min-height: 100vh;
  padding: var(--app-spacing-md);
}

@mixin centered-content($max-width: 420px) {
  width: 100%;
  max-width: $max-width;
  margin: 0 auto;
}

@mixin page-header() {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--app-spacing-xl);
  padding-top: var(--app-spacing-xl);
}

// Form Mixins
// -----------------------------------------------------------------------------
@mixin modern-input() {
  background: var(--app-gray-50);
  border: 2px solid var(--app-gray-200);
  border-radius: var(--app-radius-lg);
  transition: all var(--app-transition-normal);
  min-height: 3.5rem;
  
  &.focused {
    border-color: var(--app-primary);
    background: var(--app-white);
    box-shadow: 0 0 0 3px rgba(var(--app-primary-rgb), 0.1);
  }
  
  &.error {
    border-color: var(--app-danger);
    background: var(--app-danger-light);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }
  
  &:hover:not(.error) {
    border-color: var(--app-gray-300);
  }
}

@mixin input-icon() {
  color: var(--app-gray-400);
  font-size: var(--app-font-size-xl);
  margin-left: var(--app-spacing-md);
  margin-right: var(--app-spacing-sm);
  transition: color var(--app-transition-normal);
  flex-shrink: 0;
}

@mixin modern-button($variant: primary) {
  border-radius: var(--app-radius-lg);
  font-weight: var(--app-font-weight-semibold);
  font-size: var(--app-font-size-base);
  height: 3.5rem;
  transition: all var(--app-transition-normal);
  
  @if $variant == primary {
    --background: var(--app-gradient-secondary);
    --background-activated: var(--app-gradient-secondary);
    --background-hover: var(--app-gradient-secondary);
    --color: var(--app-white);
    box-shadow: 0 4px 14px 0 rgba(var(--app-primary-rgb), 0.4);
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 6px 20px 0 rgba(var(--app-primary-rgb), 0.5);
    }
  } @else if $variant == outline {
    --background: transparent;
    --background-hover: rgba(var(--app-primary-rgb), 0.05);
    --border-color: var(--app-primary);
    --border-width: 2px;
    --color: var(--app-primary);
    
    &:hover {
      --background: rgba(var(--app-primary-rgb), 0.1);
      transform: translateY(-1px);
    }
  } @else if $variant == danger {
    --background: transparent;
    --background-hover: var(--app-danger-light);
    --color: var(--app-danger);
    
    &:hover {
      --background: var(--app-danger-light);
      transform: translateY(-1px);
    }
  }
}

// Animation Mixins
// -----------------------------------------------------------------------------
@mixin fade-in-up($delay: 0s) {
  animation: fadeInUp 0.8s ease-out $delay both;
}

@mixin hover-lift() {
  transition: transform var(--app-transition-normal);
  
  &:hover {
    transform: translateY(-2px);
  }
}

// Responsive Mixins
// -----------------------------------------------------------------------------
@mixin mobile-only {
  @media (max-width: 480px) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: 1024px) {
    @content;
  }
}

// Keyframes
// -----------------------------------------------------------------------------
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
